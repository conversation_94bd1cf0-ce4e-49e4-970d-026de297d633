import * as Cesium from 'cesium';
import { Satellite, SatellitePosition } from '../../../types/satellite';
import { OrbitCalculator } from '../../../services/orbitCalculator';
import { SpaceDebris } from '../../../types/debris';
import { Cartesian3, JulianDate } from 'cesium';
import { formatSatelliteName } from '../../../utils/satellite';

interface Position {
  longitude: number;
  latitude: number;
  height: number;
}

export type SatelliteClickHandler = (satellite: Satellite) => void;

// 添加接口定义用于跟踪卫星的TLE数据
interface TrackedSatelliteData {
  id: string;
  name?: string;
  line1: string;
  line2: string;
}

// 添加简化版卫星接口，用于处理动态数据
interface SimpleSatellite {
  id: string;
  name: string;
  constellationName?: string;
  sampledPosition?: Cesium.SampledPositionProperty;
  satnum?: string;
  tle?: {
    line1: string;
    line2: string;
  };
}

export class SatelliteController {
  private viewer: Cesium.Viewer;
  private satellites: Map<string, Cesium.Entity> = new Map();
  private orbits: Map<string, Cesium.Entity> = new Map();
  private satelliteData: Map<string, Satellite> = new Map();
  private onSatelliteDoubleClick?: SatelliteClickHandler;
  private debrisEntities: Map<string, Cesium.Entity> = new Map();
  private debrisOrbitEntities: Map<string, Cesium.Entity> = new Map();
  private debrisData: Map<string, SpaceDebris> = new Map();
  private onDebrisDoubleClick?: (debris: SpaceDebris) => void;
  private footprints: Map<string, Cesium.Entity> = new Map();
  private sensorCones: Map<string, Cesium.Entity> = new Map();
  private lightSatelliteRenderer?: any; // 轻量级卫星渲染器的引用
  // 添加跟踪卫星列表，用于更新轨道
  private trackedSatellites: Map<string, TrackedSatelliteData> = new Map();
  // 添加轨道可见性状态变量
  private orbitsVisible: boolean = false; // 默认为关闭状态
  // 添加卫星标签显示状态变量
  private labelsVisible: boolean = true;
  // 在类的开头添加 sensorsVisible 属性
  private sensorsVisible: boolean = false; // 默认视锥不可见

  // 鼠标悬停功能相关属性
  private hoveredSatelliteId: string | null = null; // 当前悬停的卫星ID
  private hoverSatelliteEntity: Cesium.Entity | null = null; // 🌟 新增：悬停时创建的临时卫星实体
  private hoverOrbitEntity: Cesium.Entity | null = null; // 悬停时显示的轨道实体
  private hoverLabelEntity: Cesium.Entity | null = null; // 悬停时显示的标签实体
  private hoverTimeout: any = null; // 悬停延迟定时器
  private readonly HOVER_DELAY = 300; // 悬停延迟时间（毫秒）

  // 🌟 新增：单击跟踪功能相关属性
  private trackedSatelliteId: string | null = null; // 当前跟踪的卫星ID
  private trackingSatelliteEntity: Cesium.Entity | null = null; // 跟踪时创建的卫星实体
  private trackingOrbitEntity: Cesium.Entity | null = null; // 跟踪时显示的轨道实体
  private trackingLabelEntity: Cesium.Entity | null = null; // 跟踪时显示的标签实体
  private clickTimeout: any = null; // 单击延迟定时器，避免与双击冲突
  private readonly CLICK_DELAY = 100; // 单击延迟时间（毫秒），减少延迟提高响应速度
  
  // 🌟 新增：平滑过渡相关属性
  private isTransitioning: boolean = false; // 是否正在进行平滑过渡
  private readonly TRANSITION_DURATION = 2.5; // 平滑过渡时间（秒）

  // 为不同星座定义不同的颜色
  private constellationColors: { [key: string]: Cesium.Color } = {
    'starlink': Cesium.Color.CORNFLOWERBLUE,
    'beidou': Cesium.Color.RED,
    'default': Cesium.Color.WHITE
  };

  // 🌟 新增：卫星信息面板回调
  private onSatelliteInfoRequest?: (satelliteId: string, noradId: number) => void;

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
    this.trackedSatellites = new Map<string, TrackedSatelliteData>();
    
    // 禁用 Cesium 默认的双击缩放行为
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    );
    
    // 禁用默认的实体双击追踪行为
    if (this.viewer.trackedEntity) {
      this.viewer.trackedEntity = undefined;
    }
    
    // 设置事件处理器
    this.setupEventHandlers();
    
    // 初始化轨道可见性状态
    this.initializeOrbitsVisibility();
  }
  
  /**
   * 初始化轨道可见性状态
   * 确保在控制器初始化时就正确设置轨道开关状态
   */
  private initializeOrbitsVisibility(visible: boolean = false): void {
    // 设置初始可见性状态
    this.orbitsVisible = visible;
    console.log(`初始化轨道可见性状态: ${visible ? '显示' : '隐藏'}`);
  }

  /**
   * 设置卫星双击事件处理器
   */
  setOnSatelliteDoubleClick(handler: SatelliteClickHandler) {
    this.onSatelliteDoubleClick = handler;
  }

  /**
   * 🌟 新增：设置卫星信息请求回调
   */
  setOnSatelliteInfoRequest(handler: (satelliteId: string, noradId: number) => void) {
    this.onSatelliteInfoRequest = handler;
  }

  /**
   * 从TLE数据中提取NORAD ID
   * @param tle TLE数据对象
   * @returns NORAD ID，如果提取失败返回null
   */
  private extractNoradIdFromTLE(tle: { line1: string; line2: string }): number | null {
    try {
      console.log('🔍 开始提取NORAD ID，TLE数据:', tle);
      
      if (!tle || !tle.line1) {
        console.warn('❌ TLE数据无效或缺少line1');
        return null;
      }
      
      // TLE第一行格式: "1 NNNNNC NNNNNAAA NNNNN.NNNNNNNN +.NNNNNNNN +NNNNN-N +NNNNN-N N NNNNN"
      // NORAD ID在第3-7位（从1开始计数）
      const line1 = tle.line1.trim();
      console.log('🔍 TLE第一行:', line1);
      
      if (line1.length < 7 || !line1.startsWith('1 ')) {
        console.warn('❌ TLE第一行格式不正确，长度:', line1.length, '开头:', line1.substring(0, 2));
        return null;
      }
      
      // 提取NORAD ID（第3-7位，去除前导空格）
      const noradIdStr = line1.substring(2, 7).trim();
      console.log('🔍 提取的NORAD ID字符串:', noradIdStr);
      
      const noradId = parseInt(noradIdStr, 10);
      
      if (isNaN(noradId)) {
        console.warn('❌ NORAD ID解析失败:', noradIdStr);
        return null;
      }
      
      console.log('✅ 成功提取NORAD ID:', noradId);
      return noradId;
    } catch (error) {
      console.error('❌ 从TLE数据提取NORAD ID失败:', error);
      return null;
    }
  }

  /**
   * 设置轻量级卫星渲染器的引用
   * @param renderer 轻量级卫星渲染器实例
   */
  setLightSatelliteRenderer(renderer: any) {
    this.lightSatelliteRenderer = renderer;
  }

  /**
   * 从实体ID中提取基础ID
   * @param entityId 实体ID（如 "sensor-cone-starlink-3-timestamp" 或 "orbit-debris-1"）
   * @returns 基础ID（如 "starlink-3" 或 "debris-1"）
   */
  private extractBaseId(entityId: string): string {
    try {
      // 处理轨道实体ID
      if (entityId.startsWith('orbit-')) {
        const remaining = entityId.substring(6); // 移除 "orbit-" 前缀
        // 如果是碎片轨道
        if (remaining.startsWith('debris-')) {
          return remaining; // 直接返回 "debris-1" 这样的ID
        }
        // 如果是卫星轨道
        const parts = remaining.split('-');
        if (parts.length >= 3) {
          return `${parts[0]}-${parts[1]}`; // 返回 "starlink-3" 这样的ID
        }
        return parts[0];
      }
      // 处理传感器锥体ID
      else if (entityId.startsWith('sensor-cone-')) {
        const remaining = entityId.substring(12); // 移除 "sensor-cone-" 前缀
        const parts = remaining.split('-');
        if (parts.length >= 3) {
          return `${parts[0]}-${parts[1]}`;
        }
        return parts[0];
      }
      // 处理地面投影ID
      else if (entityId.startsWith('footprint-')) {
        const remaining = entityId.substring(10); // 移除 "footprint-" 前缀
        const parts = remaining.split('-');
        if (parts.length >= 3) {
          return `${parts[0]}-${parts[1]}`;
        }
        return parts[0];
      }
      // 处理碎片实体ID
      else if (entityId.startsWith('debris-')) {
        return entityId; // 直接返回完整的碎片ID
      }
      // 处理普通实体ID
      else {
        const parts = entityId.split('-');
        if (parts.length >= 3) {
          return `${parts[0]}-${parts[1]}`;
        }
        return parts[0];
      }
    } catch (error) {
      console.error('Error extracting base ID:', error, { entityId });
      return entityId; // 出错时返回原始ID
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    
    // 🌟 新增：单击事件处理 - 实现卫星跟踪功能
    handler.setInputAction((click: any) => {
      console.log('🎯 LEFT_CLICK 事件触发', { position: click.position });

      // 立即检查是否点击了卫星，避免延迟导致的问题
      const pickedObject = this.viewer.scene.pick(click.position);
      let isClickingSatellite = false;

      if (pickedObject) {
        // 检查是否是轻量级卫星点
        if (this.lightSatelliteRenderer && pickedObject.collection === this.lightSatelliteRenderer.pointCollection) {
          isClickingSatellite = true;
          console.log('🎯 检测到点击轻量级卫星点');
        }
        // 检查是否是卫星Entity
        else if (pickedObject.id && typeof pickedObject.id === 'object') {
          const entity = pickedObject.id as Cesium.Entity;
          if (entity.id && !entity.id.startsWith('hover-') && !entity.id.startsWith('tracking-') && !entity.id.startsWith('launch-site-')) {
            isClickingSatellite = true;
            console.log('🎯 检测到点击卫星Entity');
          }
        }
      }

      // 如果点击的是卫星，立即处理，不使用延迟，并阻止事件冒泡
      if (isClickingSatellite) {
        console.log('🎯 立即处理卫星单击事件');

        // 🌟 关键修复：阻止事件冒泡到document，防止触发关闭面板逻辑
        if (click.preventDefault) {
          click.preventDefault();
        }
        if (click.stopPropagation) {
          click.stopPropagation();
        }

        // 🌟 设置一个标志，告诉document点击处理器这是卫星点击
        (window as any).__satelliteClickInProgress = true;

        this.handleSingleClick(click.position);

        // 🌟 延迟清除标志，确保document事件处理器能够检测到
        setTimeout(() => {
          (window as any).__satelliteClickInProgress = false;
        }, 1000);

        return;
      }

      // 如果不是卫星，使用延迟处理（用于点击空白区域停止跟踪）
      if (this.clickTimeout) {
        clearTimeout(this.clickTimeout);
        this.clickTimeout = null;
      }

      this.clickTimeout = setTimeout(() => {
        console.log('🎯 延迟处理空白区域单击事件');
        this.handleSingleClick(click.position);
      }, this.CLICK_DELAY);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    
    handler.setInputAction((click: any) => {
      // 双击时取消单击处理
      if (this.clickTimeout) {
        clearTimeout(this.clickTimeout);
        this.clickTimeout = null;
      }

      // 阻止事件继续传播
      if (click.preventDefault) {
        click.preventDefault();
      }
      if (click.stopPropagation) {
        click.stopPropagation();
      }

      const pickedObject = this.viewer.scene.pick(click.position);
      if (!pickedObject || !pickedObject.id) return;

      // 保存当前相机状态
      const cameraPosition = this.viewer.camera.position.clone();
      const cameraHeading = this.viewer.camera.heading;
      const cameraPitch = this.viewer.camera.pitch;
      const cameraRoll = this.viewer.camera.roll;

      const entity = pickedObject.id;
      let entityId: string;

      // 检查entity的类型，正确获取entityId
      if (typeof entity === 'string') {
        // 直接是字符串ID（PointPrimitive情况）
        entityId = entity;
      } else if (entity && entity.id) {
        // 是Entity对象
        entityId = entity.id;
      } else {
        console.warn('无法获取实体ID', { entity });
        return;
      }
      
      try {
        // 提取基础ID
        const baseId = this.extractBaseId(entityId);

        console.log('Entity clicked:', {
          entityId,
          baseId
        });

        // 首先检查是否是卫星
        let foundSatellite = null;
        
        // 先在SatelliteController的数据中查找
        for (const [key, value] of this.satelliteData.entries()) {
          const storedBaseId = this.extractBaseId(key);
          if (storedBaseId === baseId) {
            foundSatellite = value;
            console.log('Found matching satellite in SatelliteController:', {
              storedBaseId,
              baseId,
              satellite: value
            });
            break;
          }
        }
        
        // 如果在SatelliteController中没找到，尝试从LightSatelliteRenderer中查找
        if (!foundSatellite && this.lightSatelliteRenderer) {
          const lightSatelliteData = this.lightSatelliteRenderer.getSatellite(entityId);
          if (lightSatelliteData) {
            console.log('Found matching satellite in LightSatelliteRenderer, promoting to Entity:', {
              entityId,
              baseId,
              satellite: lightSatelliteData
            });
            
            // 升级为完整的Entity以显示详细信息
            const promoted = this.lightSatelliteRenderer.promoteToEntity(entityId);
            if (promoted) {
              console.log(`成功升级卫星 ${entityId} 为Entity`);
              
              // 将LightSatelliteData转换为完整的Satellite格式，包含所有必需字段
              foundSatellite = {
                id: lightSatelliteData.id,
                name: lightSatelliteData.name,
                type: 'Satellite',
                constellationName: lightSatelliteData.constellation || 'Unknown',
                operator: lightSatelliteData.constellation || 'Unknown',
                purpose: {
                  primary: 'Communication',
                  detailed: 'Satellite communication services'
                },
                configuration: 'LEO', // 低地球轨道
                registryCountry: 'Unknown',
                operatorCountry: 'Unknown',
                launchInfo: {
                  launchDate: new Date().toISOString(),
                  launchSite: 'Unknown',
                  launchVehicle: 'Unknown',
                  launchMass: 0,
                  dryMass: 0,
                  expectedLifetime: 0,
                  contractor: 'Unknown',
                  launchCost: 0,
                  missionType: 'Unknown'
                },
                orbitInfo: {
                  meanMotion: 15.5, // 典型LEO卫星的平均运动
                  eccentricity: 0.001,
                  inclination: 53.0, // 典型倾角
                  rightAscension: 0,
                  argOfPerigee: 0,
                  meanAnomaly: 0,
                  epoch: new Date().toISOString(),
                  meanMotionDot: 0,
                  meanMotionDdot: 0,
                  dragCoefficient: 0,
                  orbitModel: 'SGP4',
                  perigee: 400,
                  apogee: 420
                },
                tle: lightSatelliteData.tle || { line1: '', line2: '' }
              };
            } else {
              console.error(`升级卫星 ${entityId} 为Entity失败`);
            }
          }
        }
        
        if (foundSatellite && this.onSatelliteDoubleClick) {
          console.log('Triggering satellite click handler for:', foundSatellite.name);
          console.log('Found satellite data:', foundSatellite);
          
          // 确保在回调前设置相机
          this.viewer.camera.setView({
            destination: cameraPosition,
            orientation: {
              heading: cameraHeading,
              pitch: cameraPitch,
              roll: cameraRoll
            }
          });
          
          // 禁用实体追踪
          this.viewer.trackedEntity = undefined;
          
          // 触发回调
          this.onSatelliteDoubleClick(foundSatellite);
          return;
        } else {
          console.log('No satellite click handler or satellite not found:', {
            foundSatellite: !!foundSatellite,
            hasHandler: !!this.onSatelliteDoubleClick
          });
        }
        
        // 如果不是卫星，检查是否是碎片
        const debris = this.debrisData.get(baseId);
        if (debris && this.onDebrisDoubleClick) {
          console.log('Triggering debris click handler for:', debris.name);
          
          // 确保在回调前设置相机
          this.viewer.camera.setView({
            destination: cameraPosition,
            orientation: {
              heading: cameraHeading,
              pitch: cameraPitch,
              roll: cameraRoll
            }
          });
          
          // 禁用实体追踪
          this.viewer.trackedEntity = undefined;
          
          // 触发回调
          this.onDebrisDoubleClick(debris);
          return;
        }

        console.log('No matching entity found for:', {
          entityId,
          baseId
        });

      } catch (error) {
        console.error('Error handling entity click:', error, {
          entityId,
          entity
        });
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

    // 添加鼠标移动事件处理 - 实现悬停功能
    handler.setInputAction((movement: any) => {
      this.handleMouseMove(movement.endPosition);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  /**
   * 添加卫星到场景
   * @param satellite 卫星数据对象
   * @returns 创建的卫星实体
   */
  public addSatellite(satellite: {
    id: string;
    name: string;
    satnum?: string;
    constellationName?: string;
    sampledPosition?: Cesium.SampledPositionProperty;
    tle?: { line1: string; line2: string };
    orbitsVisible?: boolean; // 添加轨道可见性参数
  }): Cesium.Entity | null {
    try {
      console.log(`添加卫星 ${satellite.name} (ID:${satellite.id}) 到场景`);
      
      // 检查是否已存在
      if (this.satellites.has(satellite.id)) {
        console.log(`卫星 ${satellite.id} 已存在，返回现有实体`);
        return this.satellites.get(satellite.id) || null;
      }
      
      // 解析并准备卫星数据
      const satData: Satellite = {
        id: satellite.id || 'unknown',
        name: satellite.name || `Satellite ${satellite.id}`,
        type: 'Satellite',
        constellationName: satellite.constellationName || 'Unknown',
        operator: satellite.constellationName || 'Unknown',
        purpose: {
          primary: 'Communication',
          detailed: 'Satellite communication services'
        },
        configuration: 'LEO',
        registryCountry: 'Unknown',
        operatorCountry: 'Unknown',
        launchInfo: {
          launchDate: new Date().toISOString(),
          launchSite: 'Unknown',
          launchVehicle: 'Unknown'
        },
        orbitInfo: {
          meanMotion: 0,
          eccentricity: 0,
          inclination: 0,
          rightAscension: 0,
          argOfPerigee: 0,
          meanAnomaly: 0,
          epoch: new Date().toISOString()
        },
        tle: satellite.tle || { line1: '', line2: '' }
      };
      
      // 使用轨道可见性参数（如果提供）或默认使用全局设置
      const useOrbitsVisible = satellite.orbitsVisible !== undefined ? 
        satellite.orbitsVisible : this.orbitsVisible;
      
      // 创建卫星实体并添加到场景
      const entity = this.createSatelliteEntity(satData, satellite.id);
      
      if (entity) {
        // 添加到视图
        this.viewer.entities.add(entity);
        
        // 保存到映射
        this.satellites.set(satellite.id, entity);
        
        // 强制为升级的卫星添加轨道路径，不管是否有sampledPosition
        if (satellite.sampledPosition || satellite.tle) {
          // 如果有预采样位置，使用它
          if (satellite.sampledPosition) {
            entity.position = satellite.sampledPosition;
          }
          
          // 为Entity添加轨道路径属性 - 强制显示完整轨道周期
          // 计算轨道周期来设置合适的显示时间
          let orbitalPeriod = 90; // 默认90分钟
          if (satellite.tle) {
            try {
              const line2 = satellite.tle.line2.trim();
              const meanMotionStr = line2.substring(52, 63).trim();
              const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
              if (!isNaN(meanMotion) && meanMotion > 0) {
                orbitalPeriod = (24 * 60) / meanMotion; // 转换为分钟
              }
            } catch (error) {
              console.warn('计算轨道周期失败:', error);
            }
          }
          
          console.log(`为卫星 ${satellite.id} 设置轨道显示，轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
          
          // 设置轨道显示时间为完整轨道周期（只使用trailTime显示历史轨迹）
          const orbitPeriodSeconds = orbitalPeriod * 60; // 轨道周期的秒数
          
          entity.path = new Cesium.PathGraphics({
            show: true, // 强制显示，不受全局设置影响
            leadTime: 0, // 不显示未来轨迹
            trailTime: orbitPeriodSeconds, // 显示完整轨道周期的历史轨迹
            width: 2,
            material: new Cesium.PolylineGlowMaterialProperty({
              glowPower: 0.3,
              taperPower: 0.3,
              color: this.getConstellationColor(satellite.constellationName).withAlpha(0.8)
            }),
            resolution: 360 // 高分辨率确保轨道平滑，每度一个点
          });
          
          console.log(`为卫星 ${satellite.id} 添加了完整轨道路径，轨道周期: ${orbitalPeriod.toFixed(2)}分钟, leadTime: 0秒, trailTime: ${orbitPeriodSeconds}秒`);
        }
        
        // 强制显示标签，不受全局设置影响
        if (entity.label) {
          entity.label.show = true; // 直接设置为true，不使用ConstantProperty
        }
        
        console.log(`成功添加卫星 ${satellite.id}，轨道强制显示: ${!!entity.path}`);
        return entity;
      } else {
        console.error(`无法为卫星 ${satellite.id} 创建实体`);
        return null;
      }
    } catch (error) {
      console.error(`添加卫星时发生错误:`, error);
      return null;
    }
  }

  /**
   * 移除卫星及其相关实体
   */
  removeSatellite(satelliteId: string) {
    try {
      // 查找所有相关的实体ID（处理可能的时间戳后缀）
      const relatedIds = Array.from(this.satellites.keys()).filter(id => id.startsWith(satelliteId));

      for (const id of relatedIds) {
        // 移除卫星实体
        const satellite = this.satellites.get(id);
        if (satellite) {
          this.viewer.entities.remove(satellite);
          this.satellites.delete(id);
        }

        // 移除轨道实体
        const orbit = this.orbits.get(id);
        if (orbit) {
          this.viewer.entities.remove(orbit);
          this.orbits.delete(id);
        }

        // 移除传感器锥体
        const sensorCone = this.sensorCones.get(id);
        if (sensorCone) {
          this.viewer.entities.remove(sensorCone);
          this.sensorCones.delete(id);
        }

        // 移除地面投影
        const footprint = this.footprints.get(id);
        if (footprint) {
          this.viewer.entities.remove(footprint);
          this.footprints.delete(id);
        }

        // 移除卫星数据
        this.satelliteData.delete(id);
        
        // 移除跟踪卫星数据
        this.trackedSatellites.delete(id);
      }

      // 还需要检查并删除以satelliteId为前缀的trackedSatellites记录
      const trackedIds = Array.from(this.trackedSatellites.keys()).filter(id => id.startsWith(satelliteId));
      for (const id of trackedIds) {
        this.trackedSatellites.delete(id);
      }
      
      console.log(`已完全移除卫星 ${satelliteId} 及其相关数据和实体`);
    } catch (error) {
      console.error('Error removing satellite:', error);
    }
  }

  /**
   * 获取星座的颜色
   */
  private getConstellationColor(constellationName?: string): Cesium.Color {
    // 检查constellationName是否存在
    if (!constellationName) {
      return this.constellationColors.default;
    }
    return this.constellationColors[constellationName.toLowerCase()] || this.constellationColors.default;
  }

  /**
   * 创建卫星实体
   */
  private createSatelliteEntity(satellite: Satellite, uniqueId: string): Cesium.Entity {
    try {
      // 获取当前时间
      const currentDate = new Date();
      const julianCurrentTime = Cesium.JulianDate.fromDate(currentDate);
      
      // 创建颜色
      const constellationName = this.determineConstellationFromName(satellite.name || '');
      const color = this.getConstellationColor(constellationName);
      
      // 保存初始结果，用于位置回调
      let result: Cesium.Cartesian3 | null = null;
      const satId = uniqueId;
      
      // 计算轨道周期
      const orbitalPeriod = this.calculateOrbitalPeriod(satellite);
      console.log(`卫星 ${satId} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
      
      // 计算当前位置作为静态位置
      const currentPosition = OrbitCalculator.calculatePosition(satellite, currentDate);
      let position: Cesium.PositionProperty;
      
      if (currentPosition && currentPosition.position) {
        // 使用静态位置避免CallbackProperty的问题
        const cartesian = Cesium.Cartesian3.fromDegrees(
          currentPosition.position.longitude,
          currentPosition.position.latitude,
          currentPosition.position.altitude
        );
        position = new Cesium.ConstantPositionProperty(cartesian, Cesium.ReferenceFrame.FIXED);
      } else {
        // 如果无法计算位置，使用默认位置
        console.warn(`无法计算卫星 ${uniqueId} 的位置，使用默认位置`);
        position = new Cesium.ConstantPositionProperty(
          Cesium.Cartesian3.fromDegrees(0, 0, 400000),
          Cesium.ReferenceFrame.FIXED
        );
      }

      // 格式化卫星名称，组合卫星名称和ID
      // 检查satellite.name是否已经是格式化过的名称(包含"-"和数字)
      let displayName;
      if (satellite.name && satellite.name.includes('-') && /.*-\d+$/.test(satellite.name)) {
        // 名称已经是格式化的，直接使用
        displayName = satellite.name;
        console.log(`使用已格式化的卫星名称: ${displayName}`);
      } else {
        // 名称未格式化，进行格式化
        displayName = formatSatelliteName(satellite.name, satellite.id);
        console.log(`格式化卫星名称: ${satellite.name} -> ${displayName}`);
      }

      // 创建卫星实体 - 设置合理的可见时间范围
      const startTime = Cesium.JulianDate.addHours(julianCurrentTime, -24, new Cesium.JulianDate()); // 24小时前
      const endTime = Cesium.JulianDate.addHours(julianCurrentTime, 24, new Cesium.JulianDate()); // 24小时后
      
      const entity = new Cesium.Entity({
        id: uniqueId,
        name: satellite.name,
        availability: new Cesium.TimeIntervalCollection([
          new Cesium.TimeInterval({
            start: startTime,
            stop: endTime
          }),
        ]),
        position: position,
        billboard: {
          image: this.createSatelliteIcon(color),
          scale: 1.0,
          eyeOffset: new Cesium.Cartesian3(0, 0, 0),
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          disableDepthTestDistance: undefined,
          heightReference: Cesium.HeightReference.NONE,
          scaleByDistance: new Cesium.NearFarScalar(1.0e3, 1.5, 2.0e6, 0.5),
        },
        label: {
          text: displayName, // 使用适当格式化的名称显示
          font: '16px sans-serif',
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          disableDepthTestDistance: undefined,
          heightReference: Cesium.HeightReference.NONE,
          fillColor: color,
          outlineColor: Cesium.Color.BLACK,
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 8.0e7),
          translucencyByDistance: new Cesium.NearFarScalar(6.0e7, 1.0, 8.0e7, 0.0),
          show: true // 升级的卫星强制显示标签
        }
      });

      // 注意：轨道路径将在addSatellite方法中根据sampledPosition添加
      // 这里不添加轨道路径，避免与addSatellite中的逻辑冲突

      // 只有在全局视锥可见状态为true时才创建视锥，否则只创建实体
      if (this.sensorsVisible) {
        // 为了兼容现有的方法签名，创建一个CallbackProperty包装器
        const callbackPosition = new Cesium.CallbackProperty(() => {
          return position.getValue(Cesium.JulianDate.now());
        }, false) as any;
        callbackPosition.referenceFrame = Cesium.ReferenceFrame.FIXED;
        
        // 创建视场圆锥体
        const sensorCone = this.createSensorCone(satellite, callbackPosition, uniqueId);
        this.sensorCones.set(uniqueId, sensorCone);
        this.viewer.entities.add(sensorCone);

        // 创建地面投影
        const footprint = this.createFootprint(satellite, callbackPosition, uniqueId);
        this.footprints.set(uniqueId, footprint);
        this.viewer.entities.add(footprint);
      }

      return entity;
    } catch (error) {
      console.error(`创建卫星实体失败 (${uniqueId}):`, error);
      
      // 创建一个简单的实体作为替代，不依赖轨道计算
      return new Cesium.Entity({
        id: uniqueId,
        name: satellite.name,
        position: Cesium.Cartesian3.fromDegrees(0, 0, 400000),
        billboard: {
          image: this.createSatelliteIcon(this.constellationColors.default),
          scale: 1.0
        },
        label: {
          text: `${satellite.name} (失败)`
        }
      });
    }
  }

  /**
   * 创建卫星图标
   */
  private createSatelliteIcon(color: Cesium.Color): string {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 24;  // 减小画布尺寸
      canvas.height = 24;
      const context = canvas.getContext('2d')!;

      // 清除画布
      context.clearRect(0, 0, canvas.width, canvas.height);

      // 在中心定义卫星图标
      const centerX = 12;
      const centerY = 12;
      const radius = 3; // 小圆点半径

      // 绘制太阳能面板（横线）
      const panelWidth = 14; // 太阳能板长度
      const panelHeight = 1; // 太阳能板宽度
      
      // 绘制太阳能面板
      context.beginPath();
      context.rect(centerX - panelWidth/2, centerY - panelHeight/2, panelWidth, panelHeight);
      context.fillStyle = color.withAlpha(0.8).toCssColorString();
      context.fill();
      
      // 添加简单的卫星圆点
      context.beginPath();
      context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      context.fillStyle = color.toCssColorString();
      context.fill();
      
      // 添加边框
      context.strokeStyle = 'white';
      context.lineWidth = 0.5;
      context.stroke();

      return canvas.toDataURL();
    } catch (error) {
      console.error('创建卫星图标失败:', error);
      // 创建一个简单的回退图标
      const fallbackCanvas = document.createElement('canvas');
      fallbackCanvas.width = 8;
      fallbackCanvas.height = 8;
      const ctx = fallbackCanvas.getContext('2d')!;
      ctx.fillStyle = color.toCssColorString();
      ctx.fillRect(0, 0, 8, 8);
      return fallbackCanvas.toDataURL();
    }
  }

  /**
   * 创建轨道实体
   */
  private createOrbitEntity(satellite: Satellite, uniqueId: string): Cesium.Entity {
    // 获取轨道颜色 - 基于卫星星座
    const constellationName = satellite.constellationName || this.determineConstellationFromName(satellite.name);
    const color = this.getConstellationColor(constellationName);

    // 计算轨道周期（分钟）
    let orbitPeriodMinutes = 90; // 默认值，大约90分钟一个轨道周期
    
    // 如果有TLE数据，尝试计算实际轨道周期
    if (satellite.tle && satellite.tle.line1 && satellite.tle.line2) {
      try {
        // 从TLE第二行提取平均运动（每天圈数）
        const tle2 = satellite.tle.line2;
        // 平均运动通常在TLE第二行的第53-63位置
        const meanMotionStr = tle2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        
        if (!isNaN(meanMotion) && meanMotion > 0) {
          // 转换为轨道周期（分钟）
          orbitPeriodMinutes = (24 * 60) / meanMotion;
          console.log(`卫星 ${uniqueId} 轨道周期: ${orbitPeriodMinutes.toFixed(2)} 分钟`);
        }
      } catch (error) {
        console.warn(`计算卫星 ${uniqueId} 轨道周期出错:`, error);
      }
    }

    // 关键修改：只使用trailTime显示一个完整轨道周期，完全禁用leadTime
    const trailTime = orbitPeriodMinutes * 60; // 完整轨道周期（秒）
    
    console.log(`为卫星 ${uniqueId} 创建轨道，仅使用trailTime=${trailTime}秒，不使用leadTime`);

    // 只使用position属性来创建轨道，避免使用可能引起类型错误的属性
    return new Cesium.Entity({
      id: `orbit-${uniqueId}`,
      name: `${satellite.name || uniqueId} Orbit`,
      // 不再使用satellite.availability等引起类型错误的属性
      path: {
        resolution: 720, // 高分辨率确保轨道平滑
        material: new Cesium.ColorMaterialProperty(color.withAlpha(0.6)), // 使用实线，透明度60%
        width: 2.0, // 轨道线宽
        // 关键设置：完全禁用leadTime (未来轨迹)，只使用trailTime (历史轨迹)
        leadTime: 0,
        trailTime: trailTime, // 只显示一个完整轨道周期
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 15000000),
        show: new Cesium.ConstantProperty(this.getOrbitsVisibility()) // 与全局轨道开关状态同步
      }
    });
  }

  /**
   * 设置卫星可见性
   */
  setSatelliteVisibility(satelliteId: string, visible: boolean) {
    try {
      // 查找所有相关的实体ID（处理可能的时间戳后缀）
      const relatedIds = Array.from(this.satellites.keys()).filter(id => id.startsWith(satelliteId));

      for (const id of relatedIds) {
        // 设置卫星实体可见性
        const satellite = this.satellites.get(id);
    if (satellite) {
      satellite.show = visible;
    }

        // 设置轨道实体可见性
        const orbit = this.orbits.get(id);
    if (orbit) {
      orbit.show = new Cesium.ConstantProperty(visible);
    }
  }

      // 如果没有找到相关实体，记录错误
      if (relatedIds.length === 0) {
        console.warn(`No entities found for satellite ID: ${satelliteId}`);
      }
    } catch (error) {
      console.error('Error setting satellite visibility:', error);
    }
  }

  /**
   * 追踪卫星
   * @param satId 卫星ID
   * @param tleData TLE数据（两行格式：行1和行2）
   * @param options 追踪选项
   * @returns 是否成功追踪
   */
  public trackSatellite(satId: string, tleData?: any, options?: any): boolean {
    try {
      console.log(`开始追踪卫星，ID: ${satId}，TLE数据: ${JSON.stringify(tleData)}，options: ${JSON.stringify(options)}`);
      
      // 验证卫星ID
      if (!satId) {
        console.error('无效的卫星ID');
        return false;
      }

      // 处理和验证TLE数据
      let line1: string = '';
      let line2: string = '';
      
      // 处理卫星名称
      let satName = options?.name || satId;
      
      if (tleData) {
        // 处理对象形式的TLE数据
        if (typeof tleData === 'object') {
          if (tleData.line1 && tleData.line2) {
            line1 = tleData.line1;
            line2 = tleData.line2;
          } else if (tleData.tle1 && tleData.tle2) {
            line1 = tleData.tle1;
            line2 = tleData.tle2;
          } else if (Array.isArray(tleData) && tleData.length >= 2) {
            line1 = tleData[0];
            line2 = tleData[1];
          } else if (tleData.tle && Array.isArray(tleData.tle) && tleData.tle.length >= 2) {
            line1 = tleData.tle[0];
            line2 = tleData.tle[1];
          }
        } 
        // 处理字符串形式的TLE数据（可能是两行合并的）
        else if (typeof tleData === 'string') {
          const lines = tleData.split('\n').map(line => line.trim()).filter(line => line.length > 0);
          if (lines.length >= 2) {
            line1 = lines[0];
            line2 = lines[1];
          }
        }
      }
      
      // 验证TLE数据
      if (!line1 || !line2) {
        console.error(`无效的TLE数据: ${JSON.stringify(tleData)}`);
        return false;
      }
      
      // 确保TLE格式正确（第一行以"1 "开头，第二行以"2 "开头）
      line1 = line1.trim();
      line2 = line2.trim();
      
      if (!line1.startsWith('1 ')) {
        line1 = `1 ${line1.startsWith('1') ? line1.substring(1).trim() : line1}`;
      }
      
      if (!line2.startsWith('2 ')) {
        line2 = `2 ${line2.startsWith('2') ? line2.substring(1).trim() : line2}`;
      }
      
      console.log(`处理后的TLE数据:
      第一行: ${line1}
      第二行: ${line2}`);
      
      // 保存卫星TLE数据 - 重要：使用卫星ID作为唯一键
      this.trackedSatellites.set(satId, {
        id: satId,
        name: satName,
        line1: line1,
        line2: line2
      });
      
      // 创建或更新卫星实体 - 同样使用卫星ID作为唯一标识
      const entity = this.addSatelliteEntity(satId, satName, {
        line1: line1,
        line2: line2
      });
      
      console.log(`卫星 ${satId} 已开始追踪`);
      return true;
    } catch (error) {
      console.error(`追踪卫星 ${satId} 时出错:`, error);
      return false;
    }
  }

  /**
   * 停止跟踪
   */
  stopTracking() {
    // 移除自动跟踪功能
    // this.viewer.trackedEntity = undefined;
  }

  /**
   * 将视角定位到所有可见卫星
   */
  zoomToSatellites() {
    // 保持当前视角，不进行自动定位
    // const visibleSatellites = Array.from(this.satellites.values())
    //   .filter(satellite => satellite.show);
    // if (visibleSatellites.length > 0) {
    //   this.viewer.zoomTo(visibleSatellites, new Cesium.HeadingPitchRange(0, -0.5, 2000000));
    // }
  }

  // 添加空间碎片
  addDebris(debris: SpaceDebris) {
    // Store the debris data
    this.debrisData.set(debris.id, debris);
    
    // Create debris entity
    const debrisEntity = this.createDebrisEntity(debris);
    this.debrisEntities.set(debris.id, debrisEntity);
    
    // Create orbit entity
    const orbitEntity = this.createDebrisOrbitEntity(debris);
    this.debrisOrbitEntities.set(debris.id, orbitEntity);

    // Add entities to the viewer
    this.viewer.entities.add(debrisEntity);
    this.viewer.entities.add(orbitEntity);

    console.log(`Added debris: ${debris.name}`, debrisEntity, orbitEntity);
  }

  // 移除空间碎片
  removeDebris(debrisId: string) {
    // 移除碎片数据
    this.debrisData.delete(debrisId);
    
    const debrisEntity = this.debrisEntities.get(debrisId);
    if (debrisEntity) {
      this.viewer.entities.remove(debrisEntity);
      this.debrisEntities.delete(debrisId);
    }

    const orbitEntity = this.debrisOrbitEntities.get(debrisId);
    if (orbitEntity) {
      this.viewer.entities.remove(orbitEntity);
      this.debrisOrbitEntities.delete(debrisId);
    }
  }

  // 修改 createDebrisEntity 方法中的 position 属性
  private createDebrisEntity(debris: SpaceDebris): Cesium.Entity {
    // 检查是否有有效的TLE数据
    if (!debris.orbitInfo.tle || debris.orbitInfo.tle.length !== 2) {
      console.error('Invalid TLE data for debris:', debris.id);
      return new Cesium.Entity({
        id: debris.id,
        name: debris.name
      });
    }

    // 获取时间轴的时间范围
    const startTime = this.viewer.clock.startTime;
    const endTime = this.viewer.clock.stopTime;

    // 创建位置回调属性，使用开普勒方程实时计算位置
    const position = new Cesium.CallbackProperty((time: Cesium.JulianDate | undefined, result?: Cesium.Cartesian3) => {
      if (!time) {
        return result || new Cesium.Cartesian3();
      }

      // 构造完整的卫星数据对象
      const satelliteData = {
        id: debris.id,
        name: debris.name,
        orbitInfo: debris.orbitInfo,
        tle: {
          line1: debris.orbitInfo.tle[0],
          line2: debris.orbitInfo.tle[1]
        }
      };
      
      // 计算实时位置
      const position = OrbitCalculator.calculatePosition(satelliteData as any, Cesium.JulianDate.toDate(time));
      if (!position || !position.eci.position) {
        return result || new Cesium.Cartesian3();
      }

      // 确保使用正确的经度（与OrbitCalculator.calculatePosition保持一致）
      // 注意：此时position.position.longitude可能已经被反转
      const lng = position.position.longitude;
      const lat = position.position.latitude;
      const alt = position.position.altitude;
      
      // 不直接使用ECI坐标，而是使用经纬度和高度创建ECEF坐标
      const cartesian = Cesium.Cartesian3.fromDegrees(
        lng,
        lat,
        alt
      );

      if (result) {
        result.x = cartesian.x;
        result.y = cartesian.y;
        result.z = cartesian.z;
        return result;
      }
      return cartesian;
    }, false) as Cesium.CallbackPositionProperty;

    // 设置参考坐标系为FIXED（地固系统）
    (position as any).referenceFrame = Cesium.ReferenceFrame.FIXED;

    const entity = new Cesium.Entity({
      id: debris.id,
      name: debris.name,
      availability: new Cesium.TimeIntervalCollection([new Cesium.TimeInterval({
        start: startTime,
        stop: endTime
      })]),
      position: position,
      point: {
        pixelSize: 10, // 增大点的大小
        color: Cesium.Color.ORANGERED, // 使用明亮的橙红色
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2.5,
        scaleByDistance: new Cesium.NearFarScalar(1.0e3, 1.5, 8.0e6, 0.8), // 增大远处可见性
      },
      label: {
        text: debris.name,
        font: '14px sans-serif', // 增大字体
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(15, 0),
        fillColor: Cesium.Color.ORANGERED, // 匹配点的颜色
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 8.0e7), // 增大标签可见距离
        disableDepthTestDistance: undefined,
        showBackground: true,
        backgroundColor: new Cesium.Color(0, 0, 0, 0.7)
      }
    });

    return entity;
  }

  // 修改 createDebrisOrbitEntity 方法
  private createDebrisOrbitEntity(debris: SpaceDebris): Cesium.Entity {
    // 检查是否有有效的TLE数据
    if (!debris.orbitInfo.tle || debris.orbitInfo.tle.length !== 2) {
      console.error('Invalid TLE data for debris:', debris.id);
      return new Cesium.Entity({
        id: `orbit-${debris.id}`,
        name: `${debris.name} Orbit`
      });
    }

    // 计算轨道周期（分钟）
    const orbitalPeriod = 1440 / debris.orbitInfo.meanMotion;
    
    // 使用当前时间计算一个完整轨道周期的轨迹
    const currentTime = Cesium.JulianDate.toDate(this.viewer.clock.currentTime);
    
    // 构造完整的卫星数据对象
    const satelliteData = {
      id: debris.id,
      name: debris.name,
      orbitInfo: {
        epoch: debris.orbitInfo.epoch,
        meanMotion: debris.orbitInfo.meanMotion,
        eccentricity: debris.orbitInfo.eccentricity,
        inclination: debris.orbitInfo.inclination,
        rightAscension: debris.orbitInfo.rightAscension,
        argumentOfPerigee: debris.orbitInfo.argumentOfPerigee,
        meanAnomaly: debris.orbitInfo.meanAnomaly
      },
      tle: {
        line1: debris.orbitInfo.tle[0],
        line2: debris.orbitInfo.tle[1]
      }
    };

    // 创建采样位置属性，使用FIXED参考系
    const sampledPosition = new Cesium.SampledPositionProperty(Cesium.ReferenceFrame.FIXED);
    
    // 设置插值选项
    sampledPosition.setInterpolationOptions({
      interpolationDegree: 5,
      interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
    });
    
    // 计算轨道点位置
    const positions = OrbitCalculator.calculateOrbitPath(
      satelliteData as any,
      currentTime,
      orbitalPeriod,
      360  // 每1度一个点
    );

    // 添加轨道点到采样位置属性中 - 使用经纬度方式添加，而不是直接使用ECI
    const startJulian = Cesium.JulianDate.fromDate(currentTime);
    for (let i = 0; i < positions.length; i++) {
      const pos = positions[i];
      const timeDelta = (i / positions.length) * orbitalPeriod * 60; // 时间偏移（秒）
      const timePoint = new Cesium.JulianDate();
      Cesium.JulianDate.addSeconds(startJulian, timeDelta, timePoint);
      
      // 使用经纬度创建地固坐标，而不是直接使用ECI
      const cartesian = Cesium.Cartesian3.fromDegrees(
        pos.position.longitude,
        pos.position.latitude,
        pos.position.altitude
      );
      
      sampledPosition.addSample(timePoint, cartesian);
    }

    // 使用正确的FIXED参考系创建轨道实体
    return new Cesium.Entity({
      id: `orbit-${debris.id}`,
      name: `${debris.name} Orbit`,
      position: sampledPosition,  // 使用采样位置属性
      path: {
        resolution: 120,
        width: 1.5,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.15,
          color: Cesium.Color.WHITE.withAlpha(0.5)
        }),
        leadTime: 0, // 不使用leadTime，避免双轨道
        trailTime: orbitalPeriod * 60 // 轨道周期（秒）
      }
    });
  }

  // 添加碎片双击事件处理器
  setOnDebrisDoubleClick(handler: (debris: SpaceDebris) => void) {
    this.onDebrisDoubleClick = handler;
  }

  /**
   * 计算卫星视场半径
   * @param satelliteHeight 卫星高度（米）
   * @param viewAngle 视场角度（度）
   * @returns 地面投影半径（米）
   */
  private calculateFootprintRadius(satelliteHeight: number, viewAngle: number = 45): number {
    const earthRadius = Cesium.Ellipsoid.WGS84.maximumRadius;
    const viewAngleRad = Cesium.Math.toRadians(viewAngle);
    const footprintRadius = satelliteHeight * Math.tan(viewAngleRad);
    return footprintRadius;
  }

  /**
   * 创建地面投影
   */
  private createFootprint(satellite: Satellite, position: Cesium.CallbackPositionProperty, uniqueId: string): Cesium.Entity {
    // 生成唯一的地面投影ID
    const footprintId = `footprint-${uniqueId}-${Date.now()}`;
    
    // 检查并移除已存在的地面投影
    const existingFootprint = this.footprints.get(uniqueId);
    if (existingFootprint) {
      this.viewer.entities.remove(existingFootprint);
      this.footprints.delete(uniqueId);
    }

    const constellationColor = this.getConstellationColor(satellite.constellationName);
    // 根据卫星类型设置不同的视场角度
    const viewAngle = satellite.constellationName.toLowerCase() === 'beidou' ? 60 : 45; // 北斗卫星使用60度视场角
    
    // 创建地面投影的位置计算回调
    const positions = new Cesium.CallbackProperty((time: Cesium.JulianDate | undefined) => {
      if (!time) return [];

      try {
        // 获取卫星位置
        const satPosition = position.getValue(time) as Cesium.Cartesian3;
        if (!satPosition || 
            !Cesium.defined(satPosition) || 
            !Cesium.defined(satPosition.x) || 
            !Cesium.defined(satPosition.y) || 
            !Cesium.defined(satPosition.z) ||
            isNaN(satPosition.x) || 
            isNaN(satPosition.y) || 
            isNaN(satPosition.z)) {
          return [];
        }

        // 计算卫星高度和地面投影半径
        const cartographic = Cesium.Cartographic.fromCartesian(satPosition);
        if (!cartographic) return [];

        const height = cartographic.height;
        if (height < 0) return [];

        const radius = this.calculateFootprintRadius(height, viewAngle);

        // 计算地面投影点
        const points: Cesium.Cartesian3[] = [];
        const segments = 360;

        // 将卫星位置转换为地心固定坐标系
        const transform = Cesium.Transforms.eastNorthUpToFixedFrame(satPosition);
        if (!transform) return [];
        
        for (let i = 0; i <= segments; i++) {
          const angle = (i / segments) * Math.PI * 2;
          const x = Math.cos(angle) * radius;
          const y = Math.sin(angle) * radius;
          
          // 创建局部坐标系中的点
          const localPoint = new Cesium.Cartesian3(x, y, 0);
          
          // 将点从局部坐标系转换到地心固定坐标系
          const worldPoint = Cesium.Matrix4.multiplyByPoint(
            transform,
            localPoint,
            new Cesium.Cartesian3()
          );

          if (!worldPoint) continue;

          // 将点投影到地球表面
          const cartographic = Cesium.Cartographic.fromCartesian(worldPoint);
          if (cartographic) {
            const surfacePoint = Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              0
            );
            if (surfacePoint) {
              points.push(surfacePoint);
            }
          }
        }

        return points;
      } catch (error) {
        console.error('Error calculating footprint positions:', error);
        return [];
      }
    }, false);

    // 创建地面投影实体
    return new Cesium.Entity({
      id: footprintId,
      name: `${satellite.name} Footprint`,
      polyline: {
        positions: positions,
        width: 3,
        material: new Cesium.PolylineDashMaterialProperty({
          color: constellationColor.withAlpha(0.8),
          dashLength: 16.0,
          dashPattern: parseInt('1111111100000000', 2)
        }),
        clampToGround: true
      },
      show: false // 默认不显示
    });
  }

  /**
   * 创建传感器锥体
   */
  private createSensorCone(satellite: Satellite, position: Cesium.CallbackPositionProperty, uniqueId: string): Cesium.Entity {
    // 生成唯一的传感器锥体ID
    const sensorConeId = `sensor-cone-${uniqueId}-${Date.now()}`;
    
    // 检查并移除已存在的传感器锥体
    const existingSensorCone = this.sensorCones.get(uniqueId);
    if (existingSensorCone) {
      this.viewer.entities.remove(existingSensorCone);
      this.sensorCones.delete(uniqueId);
    }

    // 根据卫星类型设置不同的视场角度和尺寸
    const isBeidou = satellite.constellationName.toLowerCase() === 'beidou';
    const length = isBeidou ? 800000 : 600000; // 北斗卫星锥体长度800km，其他600km
    const topRadius = isBeidou ? 400000 : 300000; // 北斗卫星锥体顶部半径400km，其他300km

    // 创建新的传感器锥体
    return new Cesium.Entity({
      id: sensorConeId,
      name: `${satellite.name} Sensor Cone`,
      position: position,
      orientation: new Cesium.CallbackProperty((time) => {
        if (!time) return undefined;
        const pos = position.getValue(time);
        if (!pos) return undefined;
        
        try {
          // 计算从卫星指向地心的方向（作为锥体的轴向）
          const up = Cesium.Cartesian3.normalize(
            Cesium.Cartesian3.negate(pos, new Cesium.Cartesian3()),
            new Cesium.Cartesian3()
          );
          
          // 计算东向作为右方向
          const east = Cesium.Cartesian3.normalize(
            Cesium.Cartesian3.cross(
              Cesium.Cartesian3.UNIT_Z,
              pos,
              new Cesium.Cartesian3()
            ),
            new Cesium.Cartesian3()
          );
          
          // 计算北向作为前方向
          const north = Cesium.Cartesian3.normalize(
            Cesium.Cartesian3.cross(up, east, new Cesium.Cartesian3()),
            new Cesium.Cartesian3()
          );
          
          // 创建旋转矩阵
          const rotationMatrix = new Cesium.Matrix3();
          Cesium.Matrix3.setColumn(rotationMatrix, 0, east, rotationMatrix);
          Cesium.Matrix3.setColumn(rotationMatrix, 1, north, rotationMatrix);
          Cesium.Matrix3.setColumn(rotationMatrix, 2, up, rotationMatrix);
          
          // 从旋转矩阵创建四元数
          return Cesium.Quaternion.fromRotationMatrix(rotationMatrix);
        } catch (error) {
          console.error('Error calculating sensor cone orientation:', error);
          return undefined;
        }
      }, false),
      cylinder: {
        length: length,
        topRadius: topRadius,
        bottomRadius: 0,   // 尖端朝向地球
        material: new Cesium.ColorMaterialProperty(
          Cesium.Color.fromAlpha(this.getConstellationColor(satellite.constellationName), 0.2)
        ),
        outline: true,
        outlineColor: this.getConstellationColor(satellite.constellationName),
        outlineWidth: 2,
        numberOfVerticalLines: 16,
        shadows: Cesium.ShadowMode.ENABLED,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 8.0e7)
      }
    });
  }

  /**
   * 获取碎片实体
   */
  getDebrisEntity(debrisId: string): Cesium.Entity | undefined {
    return this.debrisEntities.get(debrisId);
  }

  /**
   * 清理所有轨道实体
   */
  clearOrbitEntities() {
    // 清理卫星轨道
    this.orbits.forEach(orbit => {
      this.viewer.entities.remove(orbit);
    });
    this.orbits.clear();

    // 清理碎片轨道
    this.debrisOrbitEntities.forEach(orbit => {
      this.viewer.entities.remove(orbit);
    });
    this.debrisOrbitEntities.clear();
  }

  /**
   * 清理所有实体
   */
  public clearAll() {
    // 清除所有卫星实体
    for (const [id, entity] of this.satellites.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.satellites.clear();

    // 清理轨道实体
    for (const [id, entity] of this.orbits.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.orbits.clear();

    // 清理传感器锥体
    for (const [id, entity] of this.sensorCones.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.sensorCones.clear();

    // 清理地面投影
    for (const [id, entity] of this.footprints.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.footprints.clear();

    // 清理卫星数据
    this.satelliteData.clear();
    
    // 清理跟踪的卫星列表
    this.trackedSatellites.clear();
    
    // 清理碎片实体和数据
    for (const [id, entity] of this.debrisEntities.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.debrisEntities.clear();
    
    for (const [id, entity] of this.debrisOrbitEntities.entries()) {
      this.viewer.entities.remove(entity);
    }
    this.debrisOrbitEntities.clear();
    
    this.debrisData.clear();
    
    // 🌟 新增：清理跟踪状态
    this.stopTrackingSatellite();
    
    console.log('已清理所有实体和跟踪数据');
  }

  /**
   * 设置卫星视场可见性
   */
  setSatelliteSensorVisibility(satelliteId: string, visible: boolean) {
    // 查找与该卫星匹配的所有传感器锥体实体
    const satelliteEntityIds = Array.from(this.satellites.keys()).filter(id => {
      return id.startsWith(satelliteId) || id.includes(satelliteId);
    });

    // 对每个找到的卫星实体ID
    satelliteEntityIds.forEach(entityId => {
      // 查找对应的传感器锥体ID
      const sensorConeId = `sensor-cone-${entityId}`;
      const sensorCone = this.sensorCones.get(sensorConeId);

        if (sensorCone) {
        // 设置传感器锥体可见性
        const entity = this.viewer.entities.getById(sensorConeId);
        if (entity) {
          entity.show = visible;
        }
      } else if (visible) {
        // 如果需要显示但不存在传感器锥体，则创建一个
        const satelliteEntity = this.satellites.get(entityId);
        const satellite = this.satelliteData.get(this.extractBaseId(entityId));

        if (satelliteEntity && satellite) {
          // 获取卫星的位置回调属性
          const position = satelliteEntity.position as Cesium.CallbackPositionProperty;

          // 创建传感器锥体
          this.createSensorCone(satellite, position, entityId);
        }
      }

      // 同样处理投影
      const footprintId = `footprint-${entityId}`;
      const footprint = this.footprints.get(footprintId);

        if (footprint) {
        // 设置投影可见性
        const entity = this.viewer.entities.getById(footprintId);
        if (entity) {
          entity.show = visible;
        }
      } else if (visible) {
        // 如果需要显示但不存在投影，则创建一个
        const satelliteEntity = this.satellites.get(entityId);
        const satellite = this.satelliteData.get(this.extractBaseId(entityId));

        if (satelliteEntity && satellite) {
          // 获取卫星的位置回调属性
          const position = satelliteEntity.position as Cesium.CallbackPositionProperty;

          // 创建投影
          this.createFootprint(satellite, position, entityId);
        }
      }
    });
  }

  /**
   * 显示所有卫星的传感器
   */
  showAllSatelliteSensors() {
    // 设置全局视锥状态为可见
    this.sensorsVisible = true;
    
    // 获取所有卫星ID
    const allSatelliteIds = Array.from(this.satelliteData.keys());
    
    // 为每个卫星显示传感器
    allSatelliteIds.forEach(satelliteId => {
      this.setSatelliteSensorVisibility(satelliteId, true);
    });
    
    console.log(`已显示 ${allSatelliteIds.length} 个卫星的传感器`);
  }

  /**
   * 隐藏所有卫星的传感器
   */
  hideAllSatelliteSensors() {
    // 设置全局视锥状态为不可见
    this.sensorsVisible = false;
    
    // 隐藏所有已存在的传感器锥体
    for (const [id, entity] of this.sensorCones.entries()) {
      const cesiumEntity = this.viewer.entities.getById(id);
      if (cesiumEntity) {
        cesiumEntity.show = false;
      }
    }
    
    // 隐藏所有已存在的投影
    for (const [id, entity] of this.footprints.entries()) {
      const cesiumEntity = this.viewer.entities.getById(id);
      if (cesiumEntity) {
        cesiumEntity.show = false;
      }
    }
    
    console.log('已隐藏所有卫星传感器');
  }

  /**
   * 使用采样位置属性添加卫星
   */
  addSatelliteWithPosition(satellite: any, positionProperty: Cesium.SampledPositionProperty) {
    try {
      // 保存当前相机状态
      const cameraPosition = this.viewer.camera.position.clone();
      const cameraHeading = this.viewer.camera.heading;
      const cameraPitch = this.viewer.camera.pitch;
      const cameraRoll = this.viewer.camera.roll;
      
      // 生成唯一ID
      const uniqueId = `${satellite.id}-${Date.now()}`;
      
      // 推断星座名
      const constellationName = satellite.constellation || 
                               satellite.constellationName || 
                               this.determineConstellationFromName(satellite.name) ||
                               'Other';
                               
      // 创建标准化的卫星对象
      const standardSatellite = {
        id: satellite.id,
        name: satellite.name || `Sat ${satellite.id}`,
        type: 'satellite',
        constellationName: constellationName,
        constellation: constellationName,
        description: satellite.description || `Satellite ${satellite.id}`,
        tle: satellite.tle,
        // 添加一些必要的属性，即使它们是空的
        orbitInfo: {
          meanMotion: 0,
          eccentricity: 0,
          inclination: 0,
          rightAscension: 0,
          argumentOfPerigee: 0,
          meanAnomaly: 0,
          epoch: new Date().toISOString()
        }
      };
      
      // 保存卫星数据
      this.satelliteData.set(uniqueId, standardSatellite);
      
      // 获取星座颜色
      const color = this.getConstellationColor(constellationName);
      
      // 创建卫星实体
      const satelliteEntity = this.createSatelliteEntityWithPosition(
        standardSatellite,
        uniqueId,
        positionProperty,
        color
      );
      this.satellites.set(uniqueId, satelliteEntity);
      this.viewer.entities.add(satelliteEntity);
      
      // 创建轨道实体
      const orbitEntity = this.createOrbitEntityWithPosition(
        standardSatellite,
        uniqueId,
        positionProperty,
        color
      );
      this.orbits.set(uniqueId, orbitEntity);
      this.viewer.entities.add(orbitEntity);
      
      // 恢复相机状态，确保视角不会改变
      this.viewer.trackedEntity = undefined;
      this.viewer.camera.setView({
        destination: cameraPosition,
        orientation: {
          heading: cameraHeading,
          pitch: cameraPitch,
          roll: cameraRoll
        }
      });
      
      console.log(`已添加卫星 ${satellite.name || satellite.id}，使用采样位置属性`);
      return uniqueId;
      
    } catch (error) {
      console.error('添加卫星失败:', error, { satellite });
      return null;
    }
  }

  /**
   * 根据卫星名称确定其所属的星座
   */
  private determineConstellationFromName(satelliteName: string): string {
    const name = satelliteName.toLowerCase();
    
    if (name.includes('starlink')) return 'starlink';
    if (name.includes('oneweb')) return 'oneweb';
    if (name.includes('beidou') || name.includes('bei dou')) return 'beidou';
    if (name.includes('galileo')) return 'galileo';
    if (name.includes('gps')) return 'gps';
    if (name.includes('glonass')) return 'glonass';
    if (name.includes('iridium')) return 'iridium';
    if (name.includes('globalstar')) return 'globalstar';
    if (name.includes('cosmos')) return 'cosmos';
    if (name.includes('kuiper')) return 'kuiper';
    
    return 'default';
  }

  /**
   * 创建使用采样位置属性的卫星实体
   */
  private createSatelliteEntityWithPosition(
    satellite: any,
    uniqueId: string,
    positionProperty: Cesium.SampledPositionProperty,
    color: Cesium.Color
  ): Cesium.Entity {
    // 获取卫星名称
    const satelliteName = satellite.name || `Satellite ${satellite.id}`;
    // 获取卫星描述
    const satelliteDescription = satellite.description || satellite.metadata?.description || '';
    
    // 格式化卫星名称，组合卫星名称和ID
    const formattedName = formatSatelliteName(satelliteName, satellite.id);
    
    return new Cesium.Entity({
      id: uniqueId,
      name: satelliteName,
      position: positionProperty,
      billboard: {
        image: this.createSatelliteIcon(color),
        scale: 0.4,
        heightReference: Cesium.HeightReference.NONE,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        scaleByDistance: new Cesium.NearFarScalar(1.0e3, 0.5, 8.0e6, 0.15)
      },
      label: {
        text: formattedName, // 使用格式化的卫星名称
        font: '12px sans-serif',
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        outlineColor: Cesium.Color.BLACK,
        fillColor: color,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -10),
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 5000000),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: this.labelsVisible // 使用全局标签可见性设置
      },
      // 添加卫星详细信息
      description: `
        <table class="cesium-infoBox-defaultTable">
          <tr><th>ID</th><td>${satellite.id}</td></tr>
          <tr><th>Name</th><td>${satelliteName}</td></tr>
          <tr><th>Constellation</th><td>${satellite.constellation || satellite.constellationName || 'N/A'}</td></tr>
          <tr><th>Description</th><td>${satelliteDescription || 'N/A'}</td></tr>
        </table>
      `
    });
  }

  /**
   * 创建使用采样位置属性的轨道实体
   */
  private createOrbitEntityWithPosition(
    satellite: any,
    uniqueId: string,
    positionProperty: Cesium.SampledPositionProperty,
    color: Cesium.Color
  ): Cesium.Entity {
    // 获取卫星名称
    const satelliteName = satellite.name || `Satellite ${satellite.id}`;
    
    // 计算轨道周期（通过TLE数据）
    let orbitalPeriod = 90; // 默认90分钟
    if (satellite.tle && satellite.tle.line2) {
      try {
        // 从TLE第二行解析平均运动（每天圈数）
        const tle2 = satellite.tle.line2;
        const meanMotionStr = tle2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        if (!isNaN(meanMotion) && meanMotion > 0) {
          // 转换为轨道周期（分钟）
          orbitalPeriod = (24 * 60) / meanMotion;
          console.log(`轨道实体 ${uniqueId} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
        }
      } catch (error) {
        console.warn(`计算轨道周期失败:`, error);
      }
    }
    
    // 确保使用固定参考系 - 重要！
    if ((positionProperty as any).referenceFrame !== Cesium.ReferenceFrame.FIXED) {
      console.warn(`轨道实体 ${uniqueId} 参考系非FIXED，自动修正为FIXED`);
      // 尝试修正参考系
      (positionProperty as any).referenceFrame = Cesium.ReferenceFrame.FIXED;
    }
    
    // 计算合适的轨道周期（秒）
    const trailTime = orbitalPeriod * 60; // 完整轨道周期（秒）
    
    console.log(`创建轨道实体 ${uniqueId}，轨道显示设置: leadTime=0, trailTime=${trailTime}秒，确保只显示一条轨道线`);
    
    // 创建轨道实体 - 不要使用polyline，而是使用path，以确保与卫星实体的轨道样式一致
    return new Cesium.Entity({
      id: `orbit-${uniqueId}`,
      name: `Orbit of ${satelliteName}`,
      position: positionProperty, // 使用与卫星相同的位置属性
      path: {
        resolution: 720, // 高分辨率确保轨道平滑
        material: new Cesium.ColorMaterialProperty(color.withAlpha(0.6)), // 使用实线，透明度60%
        width: 2.0, // 轨道线宽
        // 关键设置：彻底禁用leadTime，只显示历史轨迹，避免双轨道问题
        leadTime: 0,
        trailTime: trailTime, // 使用完整轨道周期
        show: new Cesium.ConstantProperty(this.getOrbitsVisibility()) // 与全局轨道开关状态同步
      }
    });
  }

  /**
   * 获取跟踪的卫星列表
   */
  getTrackedSatellites(): TrackedSatelliteData[] {
    return Array.from(this.trackedSatellites.values());
  }

  /**
   * 更新卫星位置
   */
  updateSatellitePosition(satId: string, positionData: any[]) {
    if (!positionData || positionData.length === 0) {
      console.warn(`无效的卫星位置数据: ${satId}`);
      return;
    }

    try {
      // 找到对应的卫星实体
      const satelliteKey = Array.from(this.satellites.keys()).find(key => 
        key.startsWith(satId) || key.includes(satId)
      );

      if (!satelliteKey) {
        console.warn(`未找到卫星实体: ${satId}`);
        return;
      }

      const satelliteEntity = this.satellites.get(satelliteKey);
      if (!satelliteEntity) {
        console.warn(`未找到卫星实体对象: ${satId}`);
        return;
      }

      // 创建新的采样位置属性
      const positions = new Cesium.SampledPositionProperty();
      
      // 设置插值选项
      positions.setInterpolationOptions({
        interpolationDegree: 2,
        interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
      });

      // 添加位置点
      positionData.forEach(point => {
        const time = Cesium.JulianDate.fromIso8601(point.time);
        const position = Cesium.Cartesian3.fromDegrees(
          point.longitude,
          point.latitude,
          point.altitude * 1000 // 转换为米
        );
        positions.addSample(time, position);
      });

      // 更新卫星位置
      satelliteEntity.position = positions;

      // 找到对应的轨道实体
      const orbitKey = `orbit-${satelliteKey}`;
      const orbitEntity = this.orbits.get(orbitKey);

      if (orbitEntity) {
        // 从采样位置属性中获取轨道点
        const orbitPositions: Cesium.Cartesian3[] = [];
        const startTime = this.viewer.clock.startTime;
        const stopTime = this.viewer.clock.stopTime;
        const totalSeconds = Cesium.JulianDate.secondsDifference(stopTime, startTime);
        const step = Math.max(Math.floor(totalSeconds / 100), 60); // 至少每60秒一个点，最多100个点
        
        let currentTime = Cesium.JulianDate.clone(startTime);
        
        while (Cesium.JulianDate.lessThanOrEquals(currentTime, stopTime)) {
          const position = positions.getValue(currentTime);
          if (position) {
            orbitPositions.push(position);
          }
          
          // 增加时间步长
          Cesium.JulianDate.addSeconds(currentTime, step, currentTime);
        }

        // 更新轨道点
        orbitEntity.polyline = new Cesium.PolylineGraphics({
          positions: orbitPositions,
          width: 1,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2,
            color: Cesium.Color.WHITE.withAlpha(0.6)
          }),
          depthFailMaterial: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2,
            color: Cesium.Color.WHITE.withAlpha(0.3)
          }),
          show: new Cesium.ConstantProperty(this.getOrbitsVisibility()) // 与全局轨道开关状态同步
        });
      }

      console.log(`已更新卫星 ${satId} 的位置数据，包含 ${positionData.length} 个时间点`);
      
    } catch (error) {
      console.error(`更新卫星位置失败: ${satId}`, error);
    }
  }

  /**
   * 创建使用采样位置属性的卫星实体
   */
  private createSatelliteEntityWithSampledPosition(
    satellite: any, 
    uniqueId: string, 
    sampledPosition: Cesium.SampledPositionProperty
  ): Cesium.Entity {
    try {
      console.log(`创建卫星实体 ${satellite.name} (ID:${uniqueId})`);
      
      // 获取星座颜色
      const constellationName = satellite.constellationName || satellite.constellation || this.determineConstellationFromName(satellite.name || `Satellite ${uniqueId}`);
      const constellationColor = this.getConstellationColor(constellationName);
      
      console.log(`卫星 ${satellite.name} 使用颜色: ${constellationColor.toCssColorString()}`);
      
      // 简单测试采样位置
      if (!sampledPosition) {
        console.error(`卫星 ${satellite.name} 的采样位置为空!`);
      }

      // 获取位置属性的开始和结束时间
      let start = Cesium.JulianDate.fromDate(new Date());
      let stop = Cesium.JulianDate.addDays(start, 1, new Cesium.JulianDate()); // 默认为一天后
      
      // 确保有足够长的可用性时间范围，至少延长10小时
      const extendedStop = Cesium.JulianDate.addHours(stop, 10, new Cesium.JulianDate());
      
      // 格式化卫星名称，组合卫星名称和ID
      const formattedName = formatSatelliteName(satellite.name || `Satellite ${uniqueId}`, uniqueId);

      // 创建卫星实体
      const entity = new Cesium.Entity({
        id: uniqueId,
        name: satellite.name,
        description: `
          <div style="max-width: 340px; padding: 8px; color: white;">
            <h3 style="margin-top: 0; margin-bottom: 12px; color: #00bcd4;">${satellite.name}</h3>
            <table style="width: 100%; color: white; border-collapse: collapse; margin-bottom: 10px;">
              <tr><td style="padding: 4px 0; color: #aaa;">ID:</td><td>${uniqueId}</td></tr>
              <tr><td style="padding: 4px 0; color: #aaa;">星座:</td><td>${constellationName || '未知'}</td></tr>
            </table>
          </div>
        `,
        // 设置可用性时间范围
        availability: new Cesium.TimeIntervalCollection([
          new Cesium.TimeInterval({
            start: start,
            stop: extendedStop
          })
        ]),
        // 设置位置属性
        position: sampledPosition,
        // 设置图标
        billboard: {
          image: this.createSatelliteIcon(constellationColor),
          scale: 0.4,
          // 不要设置为无限大，这会导致卫星总是显示在前面而不考虑遮挡
          disableDepthTestDistance: 0,
          show: true
        },
        // 设置标签
        label: {
          text: formattedName, // 使用格式化的卫星名称
          font: '10px sans-serif', // 进一步减小字体
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 1.5,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(8, 0), // 缩小偏移
          // 设置为0而不是undefined，仍然允许被地球遮挡但显示时机会更长
          disableDepthTestDistance: 0,
          heightReference: Cesium.HeightReference.NONE,
          fillColor: constellationColor,
          outlineColor: Cesium.Color.BLACK,
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.5),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 5.0e7), // 调整显示距离
          translucencyByDistance: new Cesium.NearFarScalar(1.0e7, 1.0, 2.0e7, 0.0),
          show: this.labelsVisible // 使用全局标签可见性设置
        },
        // 设置轨道路径
        path: {
          resolution: 240, // 进一步增加轨道分辨率使轨道更平滑
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.1, // 减少光晕效果
            color: constellationColor.withAlpha(0.2), // 增加透明度使轨道更加柔和
            taperPower: 0.8
          }),
          width: 1.0, // 更细的轨道线
          leadTime: 60 * 240, // 前向轨道时间增加到240分钟
          trailTime: 60 * 240, // 后向轨道时间增加到240分钟
          show: new Cesium.ConstantProperty(this.getOrbitsVisibility()) // 与轨道开关状态同步
        },
        // 确保实体始终可见
        show: true
      });

      console.log(`成功创建卫星实体 ${satellite.name} (ID:${uniqueId}), 已明确设置为可见`);
      return entity;
    } catch (error) {
      console.error(`创建卫星实体失败: ${satellite.name}`, error);
      // 如果失败，创建一个简单的实体以确保至少有东西可以看见
      return new Cesium.Entity({
        id: uniqueId,
        name: satellite.name,
        point: {
          pixelSize: 15,
          color: Cesium.Color.RED,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          show: true
        },
        label: {
          text: `${satellite.name} (备用)`,
          font: '16px sans-serif',
          fillColor: Cesium.Color.RED,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          show: true
        },
        show: true
      });
    }
  }

  /**
   * 获取卫星的采样位置属性，如果不存在则创建一个新的
   */
  public getSatelliteSampledPosition(satId: string): Cesium.SampledPositionProperty {
    // 尝试从现有实体中获取
    const entity = this.satellites.get(satId);
    
    if (entity && entity.position instanceof Cesium.SampledPositionProperty) {
      console.log(`返回卫星 ${satId} 现有的采样位置属性`);
      // 确保现有属性使用FIXED参考系
      if ((entity.position as any).referenceFrame !== Cesium.ReferenceFrame.FIXED) {
        console.log(`修正卫星 ${satId} 采样位置属性的参考系为FIXED`);
        (entity.position as any).referenceFrame = Cesium.ReferenceFrame.FIXED;
      }
      return entity.position as Cesium.SampledPositionProperty;
    }
    
    // 如果实体不存在或位置不是SampledPositionProperty，创建新的
    console.log(`为卫星 ${satId} 创建新的采样位置属性，使用FIXED参考系`);
    
    // 创建时直接指定参考系
    const sampledPosition = new Cesium.SampledPositionProperty(Cesium.ReferenceFrame.FIXED);
    
    // 设置插值选项以获得更平滑的轨道
    sampledPosition.setInterpolationOptions({
      interpolationDegree: 5,
      interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
    });
    
    // 如果实体存在，更新其位置属性
    if (entity) {
      entity.position = sampledPosition;
    }
    
    return sampledPosition;
  }

  /**
   * 获取已追踪的卫星数据
   * @param satId 卫星ID
   * @returns 卫星TLE数据和名称，如果不存在则返回undefined
   */
  public getTrackedSatellite(satId: string): TrackedSatelliteData | undefined {
    return this.trackedSatellites.get(satId);
  }

  /**
   * 获取卫星实体
   * @param satId 卫星ID
   * @returns 卫星实体对象，如果不存在则返回undefined
   */
  public getSatelliteEntity(satId: string): Cesium.Entity | undefined {
    // 查找所有可能包含此卫星ID的实体
    const matchingIds = Array.from(this.satellites.keys()).filter(id => 
      id === satId || id.startsWith(`${satId}-`)
    );
    
    if (matchingIds.length === 0) {
      console.warn(`找不到卫星 ${satId} 的实体`);
      return undefined;
    }
    
    // 使用第一个匹配的实体
    const satelliteId = matchingIds[0];
    return this.satellites.get(satelliteId);
  }

  /**
   * 创建并添加卫星实体，但不添加轨道
   */
  public addSatelliteEntity(satId: string, satName: string, tle: { line1: string, line2: string }): Cesium.Entity | null {
    try {
      console.log(`为卫星 ${satId} (${satName}) 创建实体...`);
      
      // 先检查是否已存在相同ID的卫星实体，如果有则返回它
      const existingEntity = this.satellites.get(satId);
      if (existingEntity) {
        console.log(`卫星 ${satId} 已存在，返回现有实体`);
        return existingEntity;
      }
      
      // 创建采样位置属性 - 创建时直接指定参考系
      const sampledPosition = new Cesium.SampledPositionProperty(Cesium.ReferenceFrame.FIXED);
      
      // 设置插值选项以获得更平滑的轨道
      sampledPosition.setInterpolationOptions({
        interpolationDegree: 5,
        interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
      });
      
      // 计算轨道周期（通过TLE数据）
      let orbitalPeriod = 90; // 默认轨道周期（分钟）
      try {
        // 从TLE第二行解析平均运动（每天圈数）
        const tle2 = tle.line2.trim();
        const meanMotionStr = tle2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        
        if (!isNaN(meanMotion) && meanMotion > 0) {
          // 转换为轨道周期（分钟）
          orbitalPeriod = (24 * 60) / meanMotion;
          console.log(`卫星 ${satId} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
        }
      } catch (error) {
        console.warn(`计算卫星${satId}轨道周期失败:`, error);
      }
      
      // 确保周期至少为90分钟
      orbitalPeriod = Math.max(90, orbitalPeriod);
      
      // 轨道周期（秒）
      const periodInSeconds = orbitalPeriod * 60;
      
      // 轨道显示时间范围，确保显示至少一个完整轨道
      const orbitDisplayMinutes = Math.ceil(orbitalPeriod * 1.2); // 确保显示完整轨道
      console.log(`卫星${satId}轨道显示时间范围: ${orbitDisplayMinutes}分钟`);
      
      // 确定卫星颜色（根据星座）
      const constellationName = this.determineConstellationFromName(satName);
      const color = this.getConstellationColor(constellationName);

      // 获取当前跟踪的卫星总数
      const totalSatellites = this.getTrackedSatelliteCount();
      
      // 根据卫星数量确定轨迹显示的时间范围
      const { startDate, endDate } = this.getOrbitTimeRange(totalSatellites);
      const startJulian = Cesium.JulianDate.fromDate(startDate);
      const endJulian = Cesium.JulianDate.fromDate(endDate);
      
      console.log(`卫星可见性时间范围: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);
      console.log(`当前卫星总数: ${totalSatellites}, ${totalSatellites > 500 ? '使用短轨迹模式' : '使用标准轨迹模式'}`);
      
      // 格式化卫星名称，组合卫星名称和ID
      const formattedName = formatSatelliteName(satName, satId);

      // 创建卫星实体 - 使用卫星ID作为唯一标识符
      const entity = new Cesium.Entity({
        id: satId,
        name: satName,
        // 设置较长的可用性时间范围，确保卫星在整个时间段内都可见
        availability: new Cesium.TimeIntervalCollection([
          new Cesium.TimeInterval({
            start: startJulian,
            stop: endJulian
          })
        ]),
        position: sampledPosition,
        // 添加方向属性，使卫星朝向与运动方向一致
        orientation: new Cesium.VelocityOrientationProperty(sampledPosition),
        // 使用点+billboard组合提高可见性
        point: {
          pixelSize: 6, // 减小点的大小
          color: color,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 1.5,
          // 设置为0而不是POSITIVE_INFINITY，允许被地球遮挡
          disableDepthTestDistance: 0
        },
        billboard: {
          image: this.createSatelliteIcon(color),
          scale: 0.4, // 减小图标尺寸以适应更大的图标
          eyeOffset: new Cesium.Cartesian3(0, 0, 0),
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          // 设置为0而不是POSITIVE_INFINITY，允许被地球遮挡
          disableDepthTestDistance: 0,
          heightReference: Cesium.HeightReference.NONE,
          scaleByDistance: new Cesium.NearFarScalar(1.0e3, 0.8, 30.0e6, 0.4) // 增大远处可见距离和最小缩放比例
        },
        label: {
          text: formattedName, // 使用格式化的卫星名称
          font: '12px sans-serif', // 减小字体
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          pixelOffset: new Cesium.Cartesian2(10, 0), // 减小偏移距离
          // 设置为0而不是POSITIVE_INFINITY，允许被地球遮挡
          disableDepthTestDistance: 0,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scale: 1.0, // 减小整体比例
          showBackground: true,
          backgroundColor: new Cesium.Color(0.0, 0.0, 0.0, 0.5), // 增加背景透明度
          backgroundPadding: new Cesium.Cartesian2(5, 3), // 减小背景内边距
          show: this.labelsVisible // 使用全局标签可见性设置
        },
        path: {
          resolution: 720, // 高分辨率确保轨道平滑
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2, // 发光强度
            color: color.withAlpha(0.8), // 轨道线颜色
            taperPower: 0.5 // 边缘渐变
          }),
          width: 1.5, // 轨道线宽
          // 根据卫星数量设置轨道线显示时间
          leadTime: totalSatellites > 500 ? 10 * 60 : 60 * orbitalPeriod, // 大于500颗时只显示10分钟
          trailTime: 1, // 设为1秒，几乎不显示历史轨迹
          show: this.orbitsVisible // 直接使用布尔值而不是ConstantProperty
        }
      });

      // 添加到场景
      this.viewer.entities.add(entity);
      
      // 保存到卫星集合中
      this.satellites.set(satId, entity);
      
      // 保存卫星TLE数据到跟踪表中
      this.trackedSatellites.set(satId, {
        id: satId,
        name: satName,
        line1: tle.line1,
        line2: tle.line2
      });
      
      console.log(`卫星实体 ${satId} 创建成功，轨道周期: ${orbitalPeriod.toFixed(2)}分钟，${totalSatellites > 500 ? '仅显示10分钟轨迹' : '显示完整轨道'}`);
      
      return entity;
    } catch (error) {
      console.error(`创建卫星实体时错误:`, error);
      return null;
    }
  }

  /**
   * 根据卫星TLE数据计算轨道周期（分钟）
   * @param satellite 卫星对象
   * @returns 轨道周期（分钟）
   */
  private calculateOrbitalPeriod(satellite: Satellite): number {
    try {
      // 从TLE第二行解析平均运动（每天圈数）
      if (satellite.tle && satellite.tle.line2) {
        const tle2 = satellite.tle.line2.trim();
        const meanMotionStr = tle2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        
        if (!isNaN(meanMotion) && meanMotion > 0) {
          // 转换为轨道周期（分钟）
          const orbitalPeriod = (24 * 60) / meanMotion;
          return orbitalPeriod;
        }
      }
      
      return 90; // 默认90分钟
    } catch (error) {
      console.warn(`计算卫星轨道周期失败:`, error);
      return 90; // 默认90分钟
    }
  }



  /**
   * 获取所有正在追踪的卫星ID列表
   * @returns 卫星ID数组
   */
  public getAllTrackedSatellites(): string[] {
    return Array.from(this.trackedSatellites.keys());
  }

  /**
   * 获取选中的卫星对象
   */
  public getSelectedSatellite(): TrackedSatelliteData | undefined {
    if (!this.trackedSatelliteId) return undefined;
    return this.trackedSatellites.get(this.trackedSatelliteId);
  }

  /**
   * 设置所有轨道的显示状态
   * @param visible 是否显示轨道
   */
  public setAllOrbitsVisibility(visible: boolean): void {
    // 更新轨道可见性状态变量
    this.orbitsVisible = visible;
    
    // 设置所有卫星轨道的可见性
    this.orbits.forEach(orbit => {
      orbit.show = new Cesium.ConstantProperty(visible);
    });

    // 设置所有碎片轨道的可见性
    this.debrisOrbitEntities.forEach(orbit => {
      orbit.show = new Cesium.ConstantProperty(visible);
    });
    
    // 设置所有卫星实体的path属性可见性
    this.satellites.forEach(satellite => {
      if (satellite.path) {
        satellite.path.show = new Cesium.ConstantProperty(visible);
      }
    });
    
    // 更新场景
    this.viewer.scene.requestRender();
    
    console.log(`已${visible ? '显示' : '隐藏'}所有目标轨道轨迹`);
  }

  /**
   * 获取轨道是否可见状态
   * @returns 轨道可见性状态
   */
  public getOrbitsVisibility(): boolean {
    // 直接返回全局轨道可见性状态变量
    return this.orbitsVisible;
  }

  /**
   * 获取卫星标签的显示状态
   */
  public getSatelliteLabelsVisibility(): boolean {
    return this.labelsVisible;
  }

  /**
   * 设置所有卫星标签的显示状态
   * @param visible 是否显示标签
   */
  public setAllSatelliteLabelsVisibility(visible: boolean): void {
    // 更新标签可见性状态变量
    this.labelsVisible = visible;
    
    // 设置所有卫星标签的可见性
    this.satellites.forEach(satellite => {
      if (satellite.label) {
        satellite.label.show = visible;
      }
    });
    
    // 同样设置碎片标签的可见性
    this.debrisEntities.forEach(debris => {
      if (debris.label) {
        debris.label.show = visible;
      }
    });
    
    // 更新场景
    this.viewer.scene.requestRender();
    
    console.log(`已${visible ? '显示' : '隐藏'}所有卫星名称标签`);
  }

  /**
   * 获取所有活跃卫星的ID列表
   * @returns 卫星ID数组
   */
  public getAllSatellites(): string[] {
    // 获取所有实体ID
    const satelliteIds: string[] = [];
    
    // 遍历satellites映射获取所有ID
    this.satellites.forEach((entity, id) => {
      satelliteIds.push(id);
    });
    
    return satelliteIds;
  }

  /**
   * 获取视锥显示状态
   */
  public getSensorsVisibility(): boolean {
    return this.sensorsVisible;
  }

  /**
   * 设置视锥显示状态
   * @param visible 是否可见
   */
  public setSensorsVisibility(visible: boolean): void {
    // 更新全局视锥状态
    this.sensorsVisible = visible;
    
    if (visible) {
      // 1. 获取所有已加载的卫星实体ID
      const allSatelliteIds = Array.from(this.satellites.keys());
      
      // 2. 为每个卫星创建/显示视锥
      allSatelliteIds.forEach(satelliteId => {
        // 获取卫星实体和数据
        const satelliteEntity = this.satellites.get(satelliteId);
        const satellite = this.satelliteData.get(this.extractBaseId(satelliteId));
        
        // 检查是否已有视锥，若没有则创建
        const sensorConeId = `sensor-cone-${satelliteId}`;
        const existingSensorCone = this.viewer.entities.getById(sensorConeId);
        
        if (!existingSensorCone && satelliteEntity && satellite) {
          // 获取卫星位置
          const position = satelliteEntity.position as Cesium.CallbackPositionProperty;
          
          // 创建视锥
          const sensorCone = this.createSensorCone(satellite, position, satelliteId);
          this.sensorCones.set(sensorConeId, sensorCone);
          this.viewer.entities.add(sensorCone);
          
          // 创建地面投影
          const footprintId = `footprint-${satelliteId}`;
          const footprint = this.createFootprint(satellite, position, satelliteId);
          this.footprints.set(footprintId, footprint);
          this.viewer.entities.add(footprint);
        } 
        
        // 若已有视锥，设置为可见
        if (existingSensorCone) {
          existingSensorCone.show = true;
        }
        
        // 同样处理地面投影
        const footprintId = `footprint-${satelliteId}`;
        const existingFootprint = this.viewer.entities.getById(footprintId);
        if (existingFootprint) {
          existingFootprint.show = true;
        }
      });
      
      console.log(`已为 ${allSatelliteIds.length} 个卫星创建或显示视锥`);
    } else {
      // 隐藏所有已存在的视锥和投影
      this.hideAllSatelliteSensors();
    }
  }

  /**
   * 获取当前跟踪的卫星总数
   * @returns 当前正在跟踪的卫星数量
   */
  public getTrackedSatelliteCount(): number {
    return this.trackedSatellites.size;
  }

  /**
   * 根据卫星数量决定轨迹计算的时间范围
   * @param totalSatellites 当前显示的卫星总数
   * @returns 轨迹时间范围对象，包含开始和结束时间
   */
  public getOrbitTimeRange(totalSatellites: number): { startDate: Date, endDate: Date } {
    const now = new Date();
    
    if (totalSatellites > 500) {
      // 当卫星数量大于500时，只计算10分钟的轨迹
      const startDate = new Date(now);
      const endDate = new Date(now.getTime() + 10 * 60 * 1000); // 10分钟后
      return { startDate, endDate };
    } else {
      // 默认计算前后三天的轨迹
      const startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000); // 3天前
      const endDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);   // 3天后
      return { startDate, endDate };
    }
  }

  /**
   * 处理鼠标移动事件
   * @param position 鼠标位置
   */
  private handleMouseMove(position: Cesium.Cartesian2): void {
    // 🌟 新增：如果正在跟踪卫星，禁用悬停功能
    if (this.trackedSatelliteId) {
      return;
    }
    
    // 清除之前的悬停定时器
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }

    // 检测鼠标位置下的对象
    const pickedObject = this.viewer.scene.pick(position);
    let hoveredSatelliteId: string | null = null;

    if (pickedObject) {
      // 检查是否是轻量级卫星点
      if (this.lightSatelliteRenderer && pickedObject.collection === this.lightSatelliteRenderer.pointCollection) {
        const pointId = pickedObject.id;
        if (pointId && typeof pointId === 'string') {
          hoveredSatelliteId = pointId;
        }
      }
      // 检查是否是卫星Entity
      else if (pickedObject.id && typeof pickedObject.id === 'object') {
        const entity = pickedObject.id as Cesium.Entity;
        if (entity.id && !entity.id.startsWith('hover-') && !entity.id.startsWith('launch-site-')) {
          hoveredSatelliteId = entity.id;
        }
      }
    }

    // 如果悬停的卫星发生变化
    if (hoveredSatelliteId !== this.hoveredSatelliteId) {
      // 清除之前的悬停显示
      this.clearHoverDisplay();
      
      // 设置新的悬停卫星
      this.hoveredSatelliteId = hoveredSatelliteId;
      
      // 如果有新的悬停卫星，设置延迟显示
      if (hoveredSatelliteId) {
        this.hoverTimeout = setTimeout(() => {
          this.showHoverDisplay(hoveredSatelliteId!);
        }, this.HOVER_DELAY);
      }
    } else {
      // 如果当前没有悬停显示，但检测到了卫星，也要触发显示
      if (hoveredSatelliteId && !this.hoverOrbitEntity && !this.hoverLabelEntity) {
        this.hoverTimeout = setTimeout(() => {
          this.showHoverDisplay(hoveredSatelliteId!);
        }, this.HOVER_DELAY);
      }
    }
  }

  /**
   * 显示悬停时的轨道和标签 - 🌟 修复版本：创建惯性坐标系下的临时卫星实体
   * @param satelliteId 卫星ID
   */
  private async showHoverDisplay(satelliteId: string): Promise<void> {
    let satelliteData: any = null;
    
    // 首先尝试从lightSatelliteRenderer获取数据
    if (this.lightSatelliteRenderer) {
      const lightSatData = this.lightSatelliteRenderer.getSatellite(satelliteId);
      if (lightSatData) {
        satelliteData = lightSatData;
      }
    }
    
    // 如果没有找到，尝试从Entity获取
    if (!satelliteData) {
      const entity = this.satellites.get(satelliteId);
      if (entity) {
        const trackedData = this.trackedSatellites.get(satelliteId);
        const baseId = this.extractBaseId(satelliteId);
        const satData = this.satelliteData.get(baseId);
        
        satelliteData = {
          id: satelliteId,
          name: entity.name || satelliteId,
          tle: trackedData ? {
            line1: trackedData.line1,
            line2: trackedData.line2
          } : (satData?.tle || null),
          constellation: satData?.constellationName || this.determineConstellationFromName(entity.name || '')
        };
      }
    }
    
    if (!satelliteData || !satelliteData.tle) {
      return;
    }

    try {
      // 🌟 关键修复：1. 隐藏原始的点卫星
      if (this.lightSatelliteRenderer) {
        this.lightSatelliteRenderer.setPointVisibility(satelliteId, false);
      }
      
      // 🌟 关键修复：2. 创建惯性坐标系下的临时卫星实体和轨道，传入模拟时钟时间
      const { satelliteEntity, orbitEntity } = await this.createHoverEntitiesInInertialFrame(satelliteData, this.viewer.clock.currentTime);
      
      this.hoverSatelliteEntity = satelliteEntity;
      this.hoverOrbitEntity = orbitEntity;
      
      // 🌟 关键修复：3. 创建标签并附加到新的卫星实体上
      this.hoverLabelEntity = this.createHoverLabelForInertialSatellite(satelliteData, this.hoverSatelliteEntity);

      // 添加到场景
      this.viewer.entities.add(this.hoverSatelliteEntity);
      this.viewer.entities.add(this.hoverOrbitEntity);
      this.viewer.entities.add(this.hoverLabelEntity);
      
    } catch (error) {
      this.clearHoverDisplay(); // 出错时清理
    }
  }

  /**
   * 创建悬停时的标签
   * @param satelliteData 卫星数据
   */
  private createHoverLabel(satelliteData: any): void {
    const labelId = `hover-label-${satelliteData.id}`;
    
    this.hoverLabelEntity = new Cesium.Entity({
      id: labelId,
      name: `${satelliteData.name} Label`,
      position: satelliteData.position,
      label: {
        text: satelliteData.name,
        font: '16px sans-serif',
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        fillColor: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(15, -15),
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        showBackground: true,
        backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
        backgroundPadding: new Cesium.Cartesian2(8, 4),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: true
      }
    });

    this.viewer.entities.add(this.hoverLabelEntity);
  }

  /**
   * 创建悬停时的轨道 - 惯性坐标系版本，显示真正的椭圆轨道
   * @param satelliteData 卫星数据
   */
  private async createHoverOrbit(satelliteData: any): Promise<void> {
    if (!satelliteData.tle) {
      return;
    }

    try {
      // 直接导入satellite.js进行SGP4计算
      const satellite = await import('satellite.js');
      
      // 计算轨道周期
      let orbitalPeriod = 90; // 默认90分钟
      try {
        const line2 = satelliteData.tle.line2.trim();
        const meanMotionStr = line2.substring(52, 63).trim();
        const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
        if (!isNaN(meanMotion) && meanMotion > 0) {
          orbitalPeriod = (24 * 60) / meanMotion; // 转换为分钟
        }
      } catch (error) {
        console.warn('计算轨道周期失败:', error);
      }

      // 使用SGP4创建卫星记录
      const satrec = satellite.twoline2satrec(satelliteData.tle.line1, satelliteData.tle.line2);
      
      if (!satrec) {
        return;
      }

      console.log(`🚀 开始计算卫星 ${satelliteData.name} 的真实椭圆轨道，周期: ${orbitalPeriod.toFixed(2)} 分钟`);

      // 关键：使用惯性坐标系的ECI坐标，不受地球自转影响
      const eciPositions: Cesium.Cartesian3[] = [];
      const currentTime = new Date();
      
      // 首先计算卫星当前位置，确保轨道从卫星位置开始
      console.log(`📍 计算卫星当前位置作为轨道起点`);
      const currentPositionAndVelocity = satellite.propagate(satrec, currentTime);
      let currentEciPosition: Cesium.Cartesian3 | null = null;
      
      if (currentPositionAndVelocity.position && typeof currentPositionAndVelocity.position !== 'boolean') {
        const positionEci = currentPositionAndVelocity.position;
        currentEciPosition = new Cesium.Cartesian3(
          positionEci.x * 1000, // 转换为米
          positionEci.y * 1000,
          positionEci.z * 1000
        );
      } else {
        return;
      }
      
      // 计算采样点数量 - 确保椭圆轨道平滑
      const sampleCount = Math.max(360, Math.ceil(orbitalPeriod * 4)); // 每15秒一个点，最少360个点
      const orbitalPeriodMs = orbitalPeriod * 60 * 1000;
      const sampleInterval = orbitalPeriodMs / sampleCount;
      
      console.log(`🔄 轨道采样参数: ${sampleCount} 个点, 间隔 ${(sampleInterval / 1000).toFixed(1)} 秒`);

      let successfulPoints = 0;

      // 添加当前位置作为轨道的第一个点
      eciPositions.push(currentEciPosition);
      successfulPoints++;

      // 计算惯性空间中的轨道位置点（从当前时间开始）
      for (let i = 1; i <= sampleCount; i++) {
        const sampleTime = new Date(currentTime.getTime() + i * sampleInterval);
        
        try {
          // 使用SGP4传播器计算卫星在惯性空间中的位置
          const positionAndVelocity = satellite.propagate(satrec, sampleTime);
          
          if (positionAndVelocity.position && typeof positionAndVelocity.position !== 'boolean') {
            const positionEci = positionAndVelocity.position;
            
            // 关键：直接使用ECI坐标，保持在惯性空间中，不转换为地理坐标
            // 这样轨道就不会受到地球自转的影响，显示真正的椭圆形状
            const eciCartesian = new Cesium.Cartesian3(
              positionEci.x * 1000, // 转换为米
              positionEci.y * 1000,
              positionEci.z * 1000
            );
            
            // 验证坐标有效性
            if (!isNaN(eciCartesian.x) && !isNaN(eciCartesian.y) && !isNaN(eciCartesian.z)) {
              eciPositions.push(eciCartesian);
              successfulPoints++;
            }
          }
        } catch (error) {
          // 静默处理个别计算失败的点
          if (i % 100 === 0) {
            console.warn(`SGP4计算轨道点 ${i} 失败:`, error);
          }
        }
      }

      console.log(`✨ 惯性空间轨道计算完成: ${successfulPoints}/${sampleCount} 个有效位置点`);

      if (eciPositions.length < 50) {
        return;
      }

      // 确保轨道闭合 - 回到卫星当前位置
      if (eciPositions.length > 0 && currentEciPosition) {
        eciPositions.push(currentEciPosition); // 添加当前位置确保椭圆闭合
      }

      console.log(`🎯 创建惯性空间轨道polyline，包含 ${eciPositions.length} 个ECI位置点`);

      // 创建轨道实体，在惯性坐标系中显示真正的椭圆轨道
      this.hoverOrbitEntity = new Cesium.Entity({
        name: `${satelliteData.name} Elliptical Orbit`,
        polyline: {
          positions: eciPositions,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.4,
            color: Cesium.Color.YELLOW.withAlpha(0.9),
            taperPower: 0.2
          }),
          width: 3,
          clampToGround: false,
          show: true
        }
      });

      // 设置实体的参考坐标系为惯性坐标系
      (this.hoverOrbitEntity as any).referenceFrame = Cesium.ReferenceFrame.INERTIAL;

      // 添加到viewer
      this.viewer.entities.add(this.hoverOrbitEntity);
      
      // 强制刷新场景
      this.viewer.scene.requestRender();
      
      console.log(`🎉 椭圆轨道创建成功: ${satelliteData.name}`);
    } catch (error) {
      console.error(`❌ 创建椭圆轨道失败:`, error);
    }
  }

  /**
   * 清除悬停显示 - 🌟 修复版本：清理所有临时实体并恢复原始点
   */
  private clearHoverDisplay(): void {
    // 清理延迟定时器
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }

    // 🌟 关键修复：清理临时卫星实体
    if (this.hoverSatelliteEntity) {
      this.viewer.entities.remove(this.hoverSatelliteEntity);
      this.hoverSatelliteEntity = null;
    }

    // 移除悬停标签
    if (this.hoverLabelEntity) {
      this.viewer.entities.remove(this.hoverLabelEntity);
      this.hoverLabelEntity = null;
    }

    // 移除悬停轨道
    if (this.hoverOrbitEntity) {
      this.viewer.entities.remove(this.hoverOrbitEntity);
      this.hoverOrbitEntity = null;
    }

    // 🌟 关键修复：恢复原始点卫星的可见性
    if (this.hoveredSatelliteId && this.lightSatelliteRenderer) {
      this.lightSatelliteRenderer.setPointVisibility(this.hoveredSatelliteId, true);
    }

    // 重置悬停状态
    this.hoveredSatelliteId = null;
  }

  /**
   * 🌟 关键方法：在惯性坐标系下创建临时卫星实体和轨道
   * @param satelliteData 卫星数据
   * @param julianCurrentTime 🌟 修复：当前的儒略时间，来自viewer.clock
   * @returns 包含卫星实体和轨道实体的对象
   */
  private async createHoverEntitiesInInertialFrame(satelliteData: any, julianCurrentTime: Cesium.JulianDate): Promise<{
    satelliteEntity: Cesium.Entity;
    orbitEntity: Cesium.Entity;
  }> {
    // 导入satellite.js进行SGP4计算
    const satellite = await import('satellite.js');
    const satrec = satellite.twoline2satrec(satelliteData.tle.line1, satelliteData.tle.line2);
    
    if (!satrec) {
      throw new Error('无法创建SGP4卫星记录');
    }

    // 计算轨道周期
    let orbitalPeriod = 90; // 默认90分钟
    try {
      const line2 = satelliteData.tle.line2.trim();
      const meanMotionStr = line2.substring(52, 63).trim();
      const meanMotion = parseFloat(meanMotionStr); // 单位：圈/天
      if (!isNaN(meanMotion) && meanMotion > 0) {
        orbitalPeriod = (24 * 60) / meanMotion; // 转换为分钟
      }
    } catch (error) {
      console.warn('计算轨道周期失败:', error);
    }
    
    console.log(`📊 卫星 ${satelliteData.name} 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
    
    // 🌟 关键：创建惯性坐标系下的采样位置属性
    const sampledPosition = new Cesium.SampledPositionProperty(Cesium.ReferenceFrame.INERTIAL);
    sampledPosition.setInterpolationOptions({
      interpolationDegree: 5,
      interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
    });

    // 🌟 修复：使用viewer的当前模拟时间，而不是系统时间
    const currentTime = Cesium.JulianDate.toDate(julianCurrentTime);
    const orbitDurationMs = orbitalPeriod * 60 * 1000;
    const samples = 360; // 每度一个点，确保轨道平滑
    
    console.log(`🔄 开始计算 ${samples} 个轨道采样点，基于模拟时间:`, currentTime);
    
    // 生成完整轨道周期的位置点
    for (let i = 0; i <= samples; i++) {
      const timeOffset = (i / samples) * orbitDurationMs;
      const sampleTime = new Date(currentTime.getTime() + timeOffset);
      
      try {
        // 使用SGP4计算ECI位置
        const positionAndVelocity = satellite.propagate(satrec, sampleTime);
        
        if (positionAndVelocity.position && typeof positionAndVelocity.position !== 'boolean') {
          const positionEci = positionAndVelocity.position;
          
          // 🌟 关键：直接使用ECI坐标，保持在惯性空间中
          const eciCartesian = new Cesium.Cartesian3(
            positionEci.x * 1000, // 转换为米
            positionEci.y * 1000,
            positionEci.z * 1000
          );
          
          const julianTime = Cesium.JulianDate.fromDate(sampleTime);
          sampledPosition.addSample(julianTime, eciCartesian);
        }
      } catch (error) {
        // 静默处理个别计算失败的点
        if (i % 100 === 0) {
          console.warn(`计算轨道点 ${i} 失败:`, error);
        }
      }
    }
    
    console.log(`✅ 完成轨道采样点计算`);
    
    // 🌟 创建惯性坐标系下的卫星实体 - 增强的渐变发光效果
    const satelliteEntity = new Cesium.Entity({
      name: `${satelliteData.name} (Inertial)`,
      position: sampledPosition,
      point: {
        pixelSize: 7, // 总大小比白点(6像素)大1像素
        color: Cesium.Color.CYAN.withAlpha(0.7), // 核心也带透明度，避免过于实心
        outlineColor: Cesium.Color.CYAN.withAlpha(0.1), // 更淡的光晕，增强渐变效果
        outlineWidth: 2, // 稍微增加光晕宽度，产生更明显的渐变
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // 发光效果设置 - 增强透明度变化
        scaleByDistance: new Cesium.NearFarScalar(1.0e3, 1.3, 8.0e6, 0.5), // 轻微增强缩放效果
        translucencyByDistance: new Cesium.NearFarScalar(1.0e3, 0.8, 8.0e6, 0.6), // 增加透明度变化范围
        heightReference: Cesium.HeightReference.NONE
      }
    });
    
    // 🌟 创建惯性坐标系下的轨道实体
    const orbitEntity = new Cesium.Entity({
      name: `${satelliteData.name} Orbit (Inertial)`,
      position: sampledPosition, // 使用相同的位置属性
      path: {
        resolution: 1,
        material: Cesium.Color.YELLOW.withAlpha(0.45), // 增加透明度，从0.6改为0.3
        width: 1.5, // 🌟 修改：更细
        leadTime: orbitalPeriod * 60, // 显示接下来完整轨道周期
        trailTime: 1, // 仅保留1秒历史，等同于从卫星当前位置开始
        show: true
      }
    });
    
    console.log(`🎉 惯性坐标系实体创建完成:`);
    console.log(`   - 卫星实体: ${satelliteEntity.id}`);
    console.log(`   - 轨道实体: ${orbitEntity.id}`);
    console.log(`   - 轨道周期: ${orbitalPeriod.toFixed(2)} 分钟`);
    console.log(`   - 坐标系: INERTIAL (惯性)`);
    
    return { satelliteEntity, orbitEntity };
  }

  /**
   * 🌟 为惯性坐标系下的卫星创建标签
   * @param satelliteData 卫星数据
   * @param satelliteEntity 卫星实体
   * @returns 标签实体
   */
  private createHoverLabelForInertialSatellite(satelliteData: any, satelliteEntity: Cesium.Entity): Cesium.Entity {
    return new Cesium.Entity({
      name: `${satelliteData.name} Label (Inertial)`,
      position: satelliteEntity.position, // 使用与卫星相同的位置属性
      label: {
        text: satelliteData.name,
        font: '14px sans-serif',
        fillColor: Cesium.Color.YELLOW,
        pixelOffset: new Cesium.Cartesian2(0, -24),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.1, 0.1, 0.1, 0.7),
        backgroundPadding: new Cesium.Cartesian2(8, 6),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: true
      }
    });
  }

  /**
   * 🌟 新增：处理单击事件 - 实现卫星跟踪功能
   * @param position 鼠标点击位置
   */
  private handleSingleClick(position: Cesium.Cartesian2): void {
    // 🌟 新增：如果正在进行平滑过渡，忽略新的点击事件
    if (this.isTransitioning) {
      return;
    }
    
    // 检测点击位置下的对象
    const pickedObject = this.viewer.scene.pick(position);
    let clickedSatelliteId: string | null = null;

    if (pickedObject) {
      // 🌟 新增：检查是否是发射场实体
      if (pickedObject.id && typeof pickedObject.id === 'object') {
        const entity = pickedObject.id as Cesium.Entity;
        if (entity.id && entity.id.startsWith('launch-site-')) {
          return; // 发射场点击由其他逻辑处理，这里直接返回
        }
      }
      
      // 检查是否是轻量级卫星点
      if (this.lightSatelliteRenderer && pickedObject.collection === this.lightSatelliteRenderer.pointCollection) {
        const pointId = pickedObject.id;
        if (pointId && typeof pointId === 'string') {
          clickedSatelliteId = pointId;
        }
      }
      // 检查是否是卫星Entity
      else if (pickedObject.id && typeof pickedObject.id === 'object') {
        const entity = pickedObject.id as Cesium.Entity;
        if (entity.id && !entity.id.startsWith('hover-') && !entity.id.startsWith('tracking-') && !entity.id.startsWith('launch-site-')) {
          clickedSatelliteId = entity.id;
        }
      }
    }

    // 如果点击了卫星
    if (clickedSatelliteId) {
      // 如果点击的是当前跟踪的卫星，则停止跟踪
      if (clickedSatelliteId === this.trackedSatelliteId) {
        this.stopTrackingSatellite();
      } else {
        // 开始跟踪新的卫星
        this.startTrackingSatellite(clickedSatelliteId);
      }
    } else {
      // 点击空白区域，停止跟踪
      if (this.trackedSatelliteId) {
        this.stopTrackingSatellite();
      }
    }
  }

  /**
   * 🌟 新增：开始跟踪卫星
   * @param satelliteId 要跟踪的卫星ID
   */
  private async startTrackingSatellite(satelliteId: string): Promise<void> {
    console.log(`🚀 开始跟踪卫星: ${satelliteId}`);
    
    // 🌟 新增：检测是否需要平滑过渡
    const isTrackingSwitch = this.trackedSatelliteId !== null && this.trackedSatelliteId !== satelliteId;
    
    if (isTrackingSwitch) {
      await this.smoothTransitionToSatellite(satelliteId);
      return;
    }
    
    // 先停止之前的跟踪
    this.stopTrackingSatellite();
    
    // 清除悬停显示，避免冲突
    this.clearHoverDisplay();
    
    let satelliteData: any = null;
    
    // 获取卫星数据（复用悬停功能的逻辑）
    if (this.lightSatelliteRenderer) {
      const lightSatData = this.lightSatelliteRenderer.getSatellite(satelliteId);
      if (lightSatData) {
        satelliteData = lightSatData;
      }
    }
    
    if (!satelliteData) {
      const entity = this.satellites.get(satelliteId);
      if (entity) {
        const trackedData = this.trackedSatellites.get(satelliteId);
        const baseId = this.extractBaseId(satelliteId);
        const satData = this.satelliteData.get(baseId);
        
        satelliteData = {
          id: satelliteId,
          name: entity.name || satelliteId,
          tle: trackedData ? {
            line1: trackedData.line1,
            line2: trackedData.line2
          } : (satData?.tle || null),
          constellation: satData?.constellationName || this.determineConstellationFromName(entity.name || '')
        };
      }
    }
    
    if (!satelliteData || !satelliteData.tle) {
      console.warn(`❌ 无法跟踪卫星 ${satelliteId}: 找不到卫星数据或TLE数据`);
      return;
    }

    console.log('📋 准备触发卫星信息面板显示...');
    console.log('卫星数据:', satelliteData);
    console.log('TLE数据:', satelliteData.tle);

    // 🌟 新增：触发卫星信息面板显示
    const noradId = this.extractNoradIdFromTLE(satelliteData.tle);
    console.log('提取的NORAD ID:', noradId);
    console.log('信息请求回调是否存在:', !!this.onSatelliteInfoRequest);
    
    if (this.onSatelliteInfoRequest && noradId) {
      this.onSatelliteInfoRequest(satelliteId, noradId);
    } else {
      if (!this.onSatelliteInfoRequest) {
        console.warn('❌ 卫星信息请求回调未设置');
      }
      if (!noradId) {
        console.warn('❌ 无法提取NORAD ID');
      }
    }

    try {
      // 隐藏原始的点卫星
      if (this.lightSatelliteRenderer) {
        this.lightSatelliteRenderer.setPointVisibility(satelliteId, false);
      }
      
      // 创建跟踪实体（复用悬停功能的代码）
      const { satelliteEntity, orbitEntity } = await this.createHoverEntitiesInInertialFrame(satelliteData, this.viewer.clock.currentTime);
      
      // 🚫 不能修改Entity的id属性，因为它是只读的
      // 只修改name属性来区分跟踪和悬停
      satelliteEntity.name = `${satelliteData.name} (Tracking)`;
      orbitEntity.name = `${satelliteData.name} Orbit (Tracking)`;
      
      // 保存跟踪实体
      this.trackingSatelliteEntity = satelliteEntity;
      this.trackingOrbitEntity = orbitEntity;
      
      // 创建跟踪标签
      this.trackingLabelEntity = this.createTrackingLabel(satelliteData, this.trackingSatelliteEntity);
      
      // 添加到场景
      this.viewer.entities.add(this.trackingSatelliteEntity);
      this.viewer.entities.add(this.trackingOrbitEntity);
      this.viewer.entities.add(this.trackingLabelEntity);
      
      // 🌟 关键：设置相机跟踪
      this.viewer.trackedEntity = this.trackingSatelliteEntity;
      
      // 验证相机跟踪是否设置成功
      setTimeout(() => {
        console.log('📹 相机跟踪设置验证:');
        console.log('📹 viewer.trackedEntity:', this.viewer.trackedEntity);
        console.log('📹 viewer.trackedEntity.id:', this.viewer.trackedEntity?.id);
        console.log('📹 相机模式:', this.viewer.camera.mode);
        console.log('📹 相机位置:', this.viewer.camera.position);
        
        // 如果跟踪仍然没有生效，尝试手动飞行到卫星位置
        if (!this.viewer.trackedEntity || this.viewer.trackedEntity !== this.trackingSatelliteEntity) {
          const position = this.trackingSatelliteEntity.position?.getValue(this.viewer.clock.currentTime);
          if (position) {
            this.viewer.camera.flyTo({
              destination: position,
              duration: 2.0,
              complete: () => {
                // 飞行完成后再次尝试设置跟踪
                this.viewer.trackedEntity = this.trackingSatelliteEntity;
              }
            });
          }
        }
      }, 500);
      
      // 设置跟踪状态
      this.trackedSatelliteId = satelliteId;
      
    } catch (error) {
      console.error(`❌ 跟踪卫星 ${satelliteId} 失败:`, error);
      this.stopTrackingSatellite(); // 出错时清理
    }
  }

  /**
   * 🌟 新增：停止跟踪卫星
   */
  private stopTrackingSatellite(): void {
    console.log('🛑 停止卫星跟踪');
    
    // 停止相机跟踪
    this.viewer.trackedEntity = undefined;
    
    // 清理跟踪卫星实体
    if (this.trackingSatelliteEntity) {
      this.viewer.entities.remove(this.trackingSatelliteEntity);
      this.trackingSatelliteEntity = null;
    }
    
    // 清理跟踪轨道实体
    if (this.trackingOrbitEntity) {
      this.viewer.entities.remove(this.trackingOrbitEntity);
      this.trackingOrbitEntity = null;
    }
    
    // 清理跟踪标签实体
    if (this.trackingLabelEntity) {
      this.viewer.entities.remove(this.trackingLabelEntity);
      this.trackingLabelEntity = null;
    }
    
    // 恢复原始点卫星的可见性
    if (this.trackedSatelliteId && this.lightSatelliteRenderer) {
      this.lightSatelliteRenderer.setPointVisibility(this.trackedSatelliteId, true);
    }
    
    // 重置跟踪状态
    this.trackedSatelliteId = null;
  }

  /**
   * 🌟 新增：为跟踪的卫星创建标签
   * @param satelliteData 卫星数据
   * @param satelliteEntity 卫星实体
   * @returns 标签实体
   */
  private createTrackingLabel(satelliteData: any, satelliteEntity: Cesium.Entity): Cesium.Entity {
    return new Cesium.Entity({
      name: `${satelliteData.name} Label (Tracking)`,
      position: satelliteEntity.position, // 使用与卫星相同的位置属性
      label: {
        text: `🎯 ${satelliteData.name}`, // 添加跟踪图标
        font: '16px sans-serif', // 比悬停标签稍大
        fillColor: Cesium.Color.CYAN, // 使用青色区分跟踪状态
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(0, -30), // 稍微偏移更多
        showBackground: true,
        backgroundColor: new Cesium.Color(0.0, 0.5, 0.5, 0.8), // 青色背景
        backgroundPadding: new Cesium.Cartesian2(10, 8),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: true
      }
    });
  }

  /**
   * 🌟 新增：获取当前跟踪的卫星ID
   * @returns 当前跟踪的卫星ID，如果没有跟踪则返回null
   */
  public getTrackedSatelliteId(): string | null {
    return this.trackedSatelliteId;
  }

  /**
   * 🌟 新增：检查是否正在跟踪卫星
   * @returns 是否正在跟踪卫星
   */
  public isTrackingSatellite(): boolean {
    return this.trackedSatelliteId !== null;
  }

  /**
   * 🌟 新增：公共方法停止跟踪卫星
   */
  public stopSatelliteTracking(): void {
    this.stopTrackingSatellite();
  }

  /**
   * 🌟 新增：测试方法 - 手动跟踪第一个可用的卫星
   */
  public testTrackFirstSatellite(): void {
    console.log('🧪 测试跟踪第一个可用的卫星');
    
    // 从lightSatelliteRenderer获取第一个卫星
    if (this.lightSatelliteRenderer) {
      const satellites = this.lightSatelliteRenderer.getAllSatellites();
      if (satellites.length > 0) {
        const firstSatellite = satellites[0];
        console.log('🧪 找到第一个卫星:', firstSatellite.id, firstSatellite.name);
        this.startTrackingSatellite(firstSatellite.id);
        return;
      }
    }
    
    // 从satellites Map获取第一个卫星
    const satelliteIds = Array.from(this.satellites.keys());
    if (satelliteIds.length > 0) {
      const firstSatelliteId = satelliteIds[0];
      console.log('🧪 找到第一个Entity卫星:', firstSatelliteId);
      this.startTrackingSatellite(firstSatelliteId);
      return;
    }
    
    console.log('🧪 没有找到可用的卫星进行测试');
  }

  /**
   * 🌟 新增：测试方法 - 跟踪指定ID的卫星
   */
  public testTrackSatelliteById(satelliteId: string): void {
    this.startTrackingSatellite(satelliteId);
  }

  /**
   * 🌟 新增：测试方法 - 验证白色卫星点隐藏修复
   */
  public testHiddenSatelliteFix(): void {
    if (!this.lightSatelliteRenderer) {
      return;
    }
    
    // 获取第一个卫星进行测试
    const satellites = this.lightSatelliteRenderer.getAllSatellites();
    if (satellites.length === 0) {
      return;
    }
    
    const testSatellite = satellites[0];
    console.log(`🧪 使用卫星 ${testSatellite.id} (${testSatellite.name}) 进行测试`);
    
    // 1. 隐藏卫星
    this.lightSatelliteRenderer.setPointVisibility(testSatellite.id, false);
    
    // 2. 等待几秒后检查是否仍然隐藏
    setTimeout(() => {
      const isHidden = this.lightSatelliteRenderer!.isHidden(testSatellite.id);
      console.log(`🧪 步骤2: 检查隐藏状态 - ${testSatellite.id} 是否被隐藏: ${isHidden}`);
      
      if (isHidden) {
        console.log('✅ 修复成功：卫星保持隐藏状态');
      } else {
        console.log('❌ 修复失败：卫星重新显示了');
      }
      
      // 3. 恢复显示
      setTimeout(() => {
        console.log('🧪 步骤3: 恢复显示');
        this.lightSatelliteRenderer!.setPointVisibility(testSatellite.id, true);
        
        setTimeout(() => {
          const isStillHidden = this.lightSatelliteRenderer!.isHidden(testSatellite.id);
          console.log(`🧪 步骤4: 检查恢复状态 - ${testSatellite.id} 是否仍被隐藏: ${isStillHidden}`);
          
          if (!isStillHidden) {
            console.log('✅ 恢复成功：卫星重新显示');
          } else {
            console.log('❌ 恢复失败：卫星仍然隐藏');
          }
        }, 1000);
      }, 3000);
    }, 3000);
  }

  /**
   * 🌟 新增：平滑过渡到新的跟踪卫星
   * @param newSatelliteId 新的跟踪卫星ID
   */
  private async smoothTransitionToSatellite(newSatelliteId: string): Promise<void> {
    console.log(`🔄 开始平滑过渡到卫星: ${newSatelliteId}`);
    
    // 设置过渡状态
    this.isTransitioning = true;
    
    try {
      // 1. 获取新卫星数据
      let newSatelliteData: any = null;
      
      if (this.lightSatelliteRenderer) {
        const lightSatData = this.lightSatelliteRenderer.getSatellite(newSatelliteId);
        if (lightSatData) {
          newSatelliteData = lightSatData;
        }
      }
      
      if (!newSatelliteData) {
        const entity = this.satellites.get(newSatelliteId);
        if (entity) {
          const trackedData = this.trackedSatellites.get(newSatelliteId);
          const baseId = this.extractBaseId(newSatelliteId);
          const satData = this.satelliteData.get(baseId);
          
          newSatelliteData = {
            id: newSatelliteId,
            name: entity.name || newSatelliteId,
            tle: trackedData ? {
              line1: trackedData.line1,
              line2: trackedData.line2
            } : (satData?.tle || null),
            constellation: satData?.constellationName || this.determineConstellationFromName(entity.name || '')
          };
        }
      }
      
      if (!newSatelliteData || !newSatelliteData.tle) {
        console.warn(`❌ 无法获取新卫星 ${newSatelliteId} 的数据，取消过渡`);
        this.isTransitioning = false;
        return;
      }
      
      // 2. 创建新的跟踪实体
      const { satelliteEntity: newSatelliteEntity, orbitEntity: newOrbitEntity } = 
        await this.createHoverEntitiesInInertialFrame(newSatelliteData, this.viewer.clock.currentTime);
      
      newSatelliteEntity.name = `${newSatelliteData.name} (Tracking)`;
      newOrbitEntity.name = `${newSatelliteData.name} Orbit (Tracking)`;
      
      const newLabelEntity = this.createTrackingLabel(newSatelliteData, newSatelliteEntity);
      
      // 3. 隐藏新卫星的原始点
      if (this.lightSatelliteRenderer) {
        this.lightSatelliteRenderer.setPointVisibility(newSatelliteId, false);
      }
      
      // 4. 添加新实体到场景
      this.viewer.entities.add(newSatelliteEntity);
      this.viewer.entities.add(newOrbitEntity);
      this.viewer.entities.add(newLabelEntity);
      
      // 5. 清理旧的跟踪实体
      if (this.trackingSatelliteEntity) {
        this.viewer.entities.remove(this.trackingSatelliteEntity);
      }
      if (this.trackingOrbitEntity) {
        this.viewer.entities.remove(this.trackingOrbitEntity);
      }
      if (this.trackingLabelEntity) {
        this.viewer.entities.remove(this.trackingLabelEntity);
      }
      
      // 恢复旧卫星的原始点可见性
      if (this.trackedSatelliteId && this.lightSatelliteRenderer) {
        this.lightSatelliteRenderer.setPointVisibility(this.trackedSatelliteId, true);
      }
      
      // 6. 设置新的跟踪状态
      this.trackingSatelliteEntity = newSatelliteEntity;
      this.trackingOrbitEntity = newOrbitEntity;
      this.trackingLabelEntity = newLabelEntity;
      this.trackedSatelliteId = newSatelliteId;

      // 🌟 新增：触发卫星信息面板显示
      const noradId = this.extractNoradIdFromTLE(newSatelliteData.tle);
      console.log('提取的NORAD ID:', noradId);
      console.log('信息请求回调是否存在:', !!this.onSatelliteInfoRequest);
      
      if (this.onSatelliteInfoRequest && noradId) {
        this.onSatelliteInfoRequest(newSatelliteId, noradId);
      } else {
        if (!this.onSatelliteInfoRequest) {
          console.warn('❌ 卫星信息请求回调未设置');
        }
        if (!noradId) {
          console.warn('❌ 无法提取NORAD ID');
        }
      }
      
      // 7. 使用Cesium内置的平滑跟踪过渡
      const currentTime = this.viewer.clock.currentTime;
      const satellitePosition = newSatelliteEntity.position?.getValue(currentTime);
      
      if (satellitePosition) {
        // 计算合适的观察距离（基于卫星高度）
        const cartographic = Cesium.Cartographic.fromCartesian(satellitePosition);
        const satelliteHeight = cartographic.height;
        
        // 根据卫星高度动态计算观察距离
        let observationDistance: number;
        if (satelliteHeight < 2000000) { // LEO: < 2000km
          observationDistance = Math.max(satelliteHeight * 0.2, 20000); // 至少20km
        } else if (satelliteHeight < 20000000) { // MEO: 2000-20000km  
          observationDistance = Math.max(satelliteHeight * 0.1, 100000); // 至少100km
        } else { // GEO: > 20000km
          observationDistance = Math.max(satelliteHeight * 0.05, 500000); // 至少500km
        }
        
        console.log(`📏 卫星高度: ${(satelliteHeight/1000).toFixed(1)}km, 观察距离: ${(observationDistance/1000).toFixed(1)}km`);
        
        // 计算从地心到卫星的方向向量（径向方向）
        const earthCenter = Cesium.Cartesian3.ZERO;
        const radialDirection = Cesium.Cartesian3.subtract(satellitePosition, earthCenter, new Cesium.Cartesian3());
        Cesium.Cartesian3.normalize(radialDirection, radialDirection);
        
        // 计算相机位置：在卫星径向外侧的观察距离处
        const cameraPosition = Cesium.Cartesian3.add(
          satellitePosition,
          Cesium.Cartesian3.multiplyByScalar(radialDirection, observationDistance, new Cesium.Cartesian3()),
          new Cesium.Cartesian3()
        );
        
        // 使用flyTo方法，设置相机位置和朝向
        this.viewer.camera.flyTo({
          destination: cameraPosition,
          orientation: {
            heading: 0.0, // 朝北
            pitch: -Cesium.Math.PI_OVER_TWO, // -90度，垂直向下看
            roll: 0.0
          },
          duration: this.TRANSITION_DURATION,
          complete: () => {
            setTimeout(() => {
              this.viewer.trackedEntity = this.trackingSatelliteEntity;
            }, 100);
          },
          cancel: () => {
            this.viewer.trackedEntity = this.trackingSatelliteEntity;
          }
        });
      } else {
        console.log('⚠️ 无法获取卫星位置，直接设置跟踪');
      }
      
    } catch (error) {
      console.error('❌ 平滑过渡失败:', error);
      
      // 出错时清理状态
      this.isTransitioning = false;
      
      // 尝试恢复到正常跟踪流程
      this.stopTrackingSatellite();
      await this.startTrackingSatellite(newSatelliteId);
      return;
    }
    
    // 重置过渡状态
    setTimeout(() => {
      this.isTransitioning = false;
    }, this.TRANSITION_DURATION * 1000 + 500); // 等待过渡完成后再重置状态
  }

  /**
   * 🌟 新增：测试平滑过渡功能
   */
  public testSmoothTransition(): void {
    if (!this.lightSatelliteRenderer) {
      return;
    }
    
    const satellites = this.lightSatelliteRenderer.getAllSatellites();
    if (satellites.length < 2) {
      return;
    }
    
    const firstSatellite = satellites[0];
    const secondSatellite = satellites[1];
    
    console.log(`🧪 开始跟踪第一个卫星: ${firstSatellite.id}`);
    this.startTrackingSatellite(firstSatellite.id);
    
    // 3秒后切换到第二个卫星
    setTimeout(() => {
      console.log(`🧪 平滑切换到第二个卫星: ${secondSatellite.id}`);
      this.startTrackingSatellite(secondSatellite.id);
    }, 3000);
  }

  /**
   * 🌟 新增：公开的跟踪卫星方法，供外部调用
   * @param satelliteId 要跟踪的卫星ID
   * @returns 是否成功开始跟踪
   */
  public async trackSatelliteById(satelliteId: string): Promise<boolean> {
    try {
      console.log(`🎯 外部请求跟踪卫星: ${satelliteId}`);
      
      // 检查卫星是否存在
      const hasLightSatellite = this.lightSatelliteRenderer?.getSatellite(satelliteId);
      const hasEntitySatellite = this.satellites.has(satelliteId);
      
      if (!hasLightSatellite && !hasEntitySatellite) {
        console.warn(`❌ 卫星 ${satelliteId} 不存在，无法跟踪`);
        return false;
      }
      
      // 调用内部跟踪方法
      await this.startTrackingSatellite(satelliteId);
      
      console.log(`✅ 成功开始跟踪卫星: ${satelliteId}`);
      return true;
      
    } catch (error) {
      console.error(`❌ 跟踪卫星 ${satelliteId} 失败:`, error);
      return false;
    }
  }
} 