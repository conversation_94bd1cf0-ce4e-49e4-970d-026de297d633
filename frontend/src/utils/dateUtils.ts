/**
 * 日期时间工具函数
 * 用于处理发射时间的格式化和时区转换
 */

/**
 * 将UTC时间转换为北京时间并格式化显示
 * @param dateString UTC时间字符串
 * @returns 格式化后的北京时间字符串，包含时分秒
 */
export function formatLaunchDateTime(dateString: string): string {
  // 检查日期字符串是否有效
  if (!dateString) {
    return '日期未知';
  }

  const launchDate = new Date(dateString);
  
  // 检查日期是否有效
  if (isNaN(launchDate.getTime())) {
    return '日期未知';
  }

  // 转换为北京时间 (UTC+8)
  const beijingTime = new Date(launchDate.getTime() + (8 * 60 * 60 * 1000));
  
  // 格式化日期部分
  const dateOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Shanghai'
  };
  
  // 格式化时间部分
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Shanghai',
    hour12: false
  };

  const formattedDate = launchDate.toLocaleDateString('zh-CN', dateOptions);
  const formattedTime = launchDate.toLocaleTimeString('zh-CN', timeOptions);
  
  return `${formattedDate} ${formattedTime}`;
}

/**
 * 格式化发射日期，包含相对时间信息
 * @param dateString UTC时间字符串
 * @returns 格式化后的日期字符串，包含相对时间信息
 */
export function formatLaunchDateWithRelativeTime(dateString: string): string {
  // 检查日期字符串是否有效
  if (!dateString) {
    return '日期未知';
  }

  const launchDate = new Date(dateString);
  const now = new Date();

  // 检查日期是否有效
  if (isNaN(launchDate.getTime())) {
    return '日期未知';
  }

  // 获取格式化的北京时间
  const formattedDateTime = formatLaunchDateTime(dateString);

  // 计算日期差异（天数）
  const diffTime = launchDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // 根据日期差异添加额外信息
  if (diffDays > 0) {
    return `${formattedDateTime} (还有 ${diffDays} 天)`;
  } else if (diffDays === 0) {
    return `${formattedDateTime} (今天)`;
  } else {
    return formattedDateTime; // 对于过去的日期，只显示日期时间
  }
}

/**
 * 检查日期是否为今天
 * @param dateString 日期字符串
 * @returns 是否为今天
 */
export function isToday(dateString: string): boolean {
  if (!dateString) return false;
  
  const date = new Date(dateString);
  const today = new Date();
  
  return date.toDateString() === today.toDateString();
}

/**
 * 获取相对时间描述
 * @param dateString 日期字符串
 * @returns 相对时间描述
 */
export function getRelativeTimeDescription(dateString: string): string {
  if (!dateString) return '';
  
  const launchDate = new Date(dateString);
  const now = new Date();
  
  if (isNaN(launchDate.getTime())) {
    return '';
  }
  
  const diffTime = launchDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays > 0) {
    return `还有 ${diffDays} 天`;
  } else if (diffDays === 0) {
    return '今天';
  } else if (diffDays === -1) {
    return '昨天';
  } else {
    return `${Math.abs(diffDays)} 天前`;
  }
}

/**
 * 检查发射时间是否在当前北京时间之后
 * @param launchDateString UTC时间字符串
 * @returns 是否在未来
 */
export function isLaunchInFuture(launchDateString: string): boolean {
  if (!launchDateString) return false;
  
  const launchDate = new Date(launchDateString);
  
  // 检查日期是否有效
  if (isNaN(launchDate.getTime())) {
    return false;
  }
  
  // 获取当前北京时间
  const now = new Date();
  const beijingNow = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  
  // 将发射时间转换为北京时间进行比较
  const beijingLaunchTime = new Date(launchDate.getTime() + (8 * 60 * 60 * 1000));
  
  // 比较时间，精确到毫秒
  return beijingLaunchTime.getTime() > beijingNow.getTime();
}

/**
 * 获取当前北京时间
 * @returns 北京时间Date对象
 */
export function getCurrentBeijingTime(): Date {
  const now = new Date();
  return new Date(now.getTime() + (8 * 60 * 60 * 1000));
}

// 搜索历史相关类型定义
export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: Date;
  type?: string;
}

export type TimeGroup = 'today' | 'lastWeek' | 'older';

export function isLastWeek(date: Date): boolean {
  const today = new Date();
  const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  return date > lastWeek && !isToday(date.toISOString());
}

export function groupSearchHistory(items: SearchHistoryItem[]): Record<TimeGroup, SearchHistoryItem[]> {
  return items.reduce((groups, item) => {
    if (isToday(item.timestamp.toISOString())) {
      groups.today.push(item);
    } else if (isLastWeek(item.timestamp)) {
      groups.lastWeek.push(item);
    } else {
      groups.older.push(item);
    }
    return groups;
  }, {
    today: [] as SearchHistoryItem[],
    lastWeek: [] as SearchHistoryItem[],
    older: [] as SearchHistoryItem[]
  });
}

/**
 * 将UTC时间转换为北京时间并格式化为新闻显示格式
 * @param utcDateString UTC时间字符串
 * @returns 格式化的北京时间字符串 (YYYY-MM-DD HH:MM:SS)
 */
export function formatNewsDateTime(utcDateString: string): string {
  if (!utcDateString) {
    return '';
  }

  const date = new Date(utcDateString);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return utcDateString; // 如果无法解析，返回原始字符串
  }

  // 转换为北京时间 (UTC+8)
  const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
  
  const year = beijingTime.getFullYear();
  const month = String(beijingTime.getMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getDate()).padStart(2, '0');
  const hours = String(beijingTime.getHours()).padStart(2, '0');
  const minutes = String(beijingTime.getMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}