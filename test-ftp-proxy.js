#!/usr/bin/env node

/**
 * FTP图片代理服务测试脚本
 * 用于测试FTP连接和图片下载功能
 */

const { Client } = require('basic-ftp');

// FTP服务器配置
const FTP_CONFIG = {
  host: '**************',
  user: 'yulingjing',
  password: 'readnewsjpg123',
  secure: false
};

async function testFTPConnection() {
  console.log('🔍 测试FTP连接...');
  
  const client = new Client();
  client.ftp.verbose = true; // 启用详细日志用于调试
  
  try {
    // 连接到FTP服务器
    console.log(`📡 连接到FTP服务器: ${FTP_CONFIG.host}`);
    await client.access(FTP_CONFIG);
    console.log('✅ FTP连接成功!');
    
    // 列出根目录内容
    console.log('\n📁 列出根目录内容:');
    const list = await client.list();
    list.forEach(item => {
      console.log(`  ${item.type === 1 ? '📁' : '📄'} ${item.name} (${item.size} bytes)`);
    });
    
    // 尝试进入一个可能的图片目录
    console.log('\n🔍 查找图片文件...');
    
    // 递归查找图片文件（限制深度避免无限递归）
    await findImageFiles(client, '.', 0, 2);
    
  } catch (error) {
    console.error('❌ FTP测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    try {
      client.close();
      console.log('\n🔌 FTP连接已关闭');
    } catch (closeError) {
      console.error('关闭连接时出错:', closeError.message);
    }
  }
}

async function findImageFiles(client, dir, currentDepth, maxDepth) {
  if (currentDepth >= maxDepth) {
    return;
  }
  
  try {
    console.log(`\n📂 检查目录: ${dir}`);
    const list = await client.list(dir);
    
    for (const item of list) {
      const fullPath = dir === '.' ? item.name : `${dir}/${item.name}`;
      
      if (item.type === 1) { // 目录
        // 递归检查子目录
        await findImageFiles(client, fullPath, currentDepth + 1, maxDepth);
      } else if (item.type === 2) { // 文件
        // 检查是否是图片文件
        const ext = item.name.toLowerCase().split('.').pop();
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) {
          console.log(`  🖼️  找到图片: ${fullPath} (${item.size} bytes)`);
          
          // 如果是第一个找到的图片，尝试下载测试
          if (currentDepth === 0) {
            await testImageDownload(client, fullPath);
          }
        }
      }
    }
  } catch (error) {
    console.log(`  ⚠️  无法访问目录 ${dir}: ${error.message}`);
  }
}

async function testImageDownload(client, imagePath) {
  console.log(`\n📥 测试下载图片: ${imagePath}`);
  
  try {
    const chunks = [];
    
    await client.downloadTo({
      write: (chunk) => {
        chunks.push(chunk);
      },
      end: () => {},
      destroy: () => {}
    }, imagePath);
    
    const imageBuffer = Buffer.concat(chunks);
    console.log(`✅ 图片下载成功! 大小: ${imageBuffer.length} bytes`);
    
    // 检查图片头部信息
    if (imageBuffer.length > 0) {
      const header = imageBuffer.slice(0, 10);
      console.log(`📊 图片头部信息: ${header.toString('hex')}`);
      
      // 简单的图片格式检测
      if (header[0] === 0xFF && header[1] === 0xD8) {
        console.log('🎯 检测到JPEG格式');
      } else if (header[0] === 0x89 && header[1] === 0x50) {
        console.log('🎯 检测到PNG格式');
      } else {
        console.log('❓ 未知图片格式');
      }
    }
    
    return true;
  } catch (error) {
    console.error(`❌ 图片下载失败: ${error.message}`);
    return false;
  }
}

async function testProxyServer() {
  console.log('\n🌐 测试代理服务器...');
  
  try {
    const response = await fetch('http://localhost:3002/health');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 代理服务器运行正常:', data);
    } else {
      console.log('⚠️  代理服务器响应异常:', response.status);
    }
  } catch (error) {
    console.log('❌ 代理服务器不可用:', error.message);
    console.log('💡 请先启动代理服务器: npm run dev:proxy');
  }
}

// 主函数
async function main() {
  console.log('🚀 FTP图片代理服务测试开始\n');
  
  // 测试FTP连接
  await testFTPConnection();
  
  // 测试代理服务器
  await testProxyServer();
  
  console.log('\n✨ 测试完成!');
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = {
  testFTPConnection,
  testProxyServer,
  FTP_CONFIG
};
