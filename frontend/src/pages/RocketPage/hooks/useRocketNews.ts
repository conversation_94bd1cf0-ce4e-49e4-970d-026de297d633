import { useState, useEffect, useCallback } from 'react';
import { NewsItem } from '../types';
import { newsService, ESNewsItem } from '../../../services/newsService';
import { formatNewsDateTime } from '../../../utils/dateUtils';

export function useRocketNews(rocketName: string) {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 10;

  const fetchNews = useCallback(async (currentPage: number) => {
    try {
      setIsLoading(true);

      // 构建请求参数
      const requestParams = {
        page: currentPage,
        limit: pageSize,
        keywords: [rocketName],
        keywordMatchType: 'substring' as const
      };

      console.log(`[useRocketNews] Fetching news for rocket: ${rocketName}, page: ${currentPage}`);
      const response = await newsService.getNewsList(requestParams);

      if (response.success && response.data.hits) {
        // 保存总数，用于判断是否还有更多
        setTotalItems(response.data.total);
        
        // 将API返回的数据转换为应用所需的格式
        const apiNews = response.data.hits.map((item: ESNewsItem) => {
          // 格式化发布日期，包含时分秒
          const utcDate = new Date(item.publish_date.year, item.publish_date.month - 1, item.publish_date.day, item.publish_date.hour, item.publish_date.minute, item.publish_date.second);
          const publishDate = formatNewsDateTime(utcDate.toISOString());

          // 处理themes_cn字段，确保它是一个有效的数组
          let themesData: string[] = [];
          if (item.themes_cn && Array.isArray(item.themes_cn)) {
            themesData = item.themes_cn;
          } else if (typeof item.themes_cn === 'string') {
            try {
              const parsed = JSON.parse(item.themes_cn);
              if (Array.isArray(parsed)) {
                themesData = parsed;
              }
            } catch (e) {
              if (item.themes_cn) {
                themesData = [item.themes_cn];
              }
            }
          }

          return {
            id: item._id,
            title: item.title_cn || item.title,
            date: publishDate,
            summary: item.summary_cn || item.summary,
            imageUrl: item.thumbnail_url || '',
            tags: themesData,
            themes_cn: themesData,
            author: {
              name: item.author || '未知作者'
            },
            source: item.source,
            originalData: item // 保存原始数据以便详情页使用
          };
        });

        if (currentPage === 1) {
          // 第一页，直接设置新闻列表
          setNews(apiNews);
        } else {
          // 不是第一页，追加到现有列表
          setNews(prevNews => [...prevNews, ...apiNews]);
        }

        // 判断是否还有更多新闻
        setHasMore(response.data.total > (currentPage * pageSize));
      } else {
        console.error('Failed to fetch rocket news:', response);
      }
    } catch (error) {
      console.error('Error fetching rocket news:', error);
    } finally {
      setIsLoading(false);
    }
  }, [rocketName, pageSize]);

  // 初始加载
  useEffect(() => {
    setPage(1);
    fetchNews(1);
  }, [fetchNews]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNews(nextPage);
    }
  }, [fetchNews, hasMore, isLoading, page]);

  return {
    news,
    isLoading,
    hasMore,
    loadMore,
    totalItems
  };
}