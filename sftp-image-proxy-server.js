const express = require('express');
const cors = require('cors');
const SftpClient = require('ssh2-sftp-client');
const path = require('path');

const app = express();
const PORT = process.env.IMAGE_PROXY_PORT || 3002;

// SFTP服务器配置
const SFTP_CONFIG = {
  host: '**************',
  port: 22, // SFTP默认端口
  username: 'yulingjing',
  password: 'readnewsjpg123',
  readyTimeout: 20000, // 20秒连接超时
  retries: 3
};

// 内存缓存配置
const imageCache = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存
const MAX_CACHE_SIZE = 100; // 最大缓存100张图片

// 启用CORS
app.use(cors({
  origin: ['http://localhost:5175', 'http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of imageCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      imageCache.delete(key);
      console.log(`[Cache] 清理过期缓存: ${key}`);
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60 * 60 * 1000); // 每小时清理一次

// 获取图片的路由
app.get('/image', async (req, res) => {
  const { path: imagePath } = req.query;
  
  if (!imagePath) {
    return res.status(400).json({ 
      error: 'Missing image path parameter',
      message: '缺少图片路径参数'
    });
  }

  console.log(`[SFTP Proxy] 请求图片: ${imagePath}`);

  // 检查缓存
  const cacheKey = imagePath;
  const cachedImage = imageCache.get(cacheKey);
  
  if (cachedImage && (Date.now() - cachedImage.timestamp < CACHE_DURATION)) {
    console.log(`[Cache] 命中缓存: ${imagePath}`);
    res.set({
      'Content-Type': cachedImage.contentType,
      'Cache-Control': 'public, max-age=86400', // 24小时浏览器缓存
      'X-Cache': 'HIT'
    });
    return res.send(cachedImage.data);
  }

  // 创建SFTP客户端
  const sftp = new SftpClient();

  try {
    // 连接到SFTP服务器
    console.log(`[SFTP] 连接到服务器: ${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`);
    await sftp.connect(SFTP_CONFIG);
    console.log(`[SFTP] 连接成功`);

    // 下载图片
    console.log(`[SFTP] 下载图片: ${imagePath}`);
    const imageBuffer = await sftp.get(imagePath);
    
    if (!imageBuffer || imageBuffer.length === 0) {
      throw new Error('图片文件为空或不存在');
    }

    // 确定内容类型
    const ext = path.extname(imagePath).toLowerCase();
    let contentType = 'image/jpeg'; // 默认
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }

    // 缓存图片（如果缓存未满）
    if (imageCache.size < MAX_CACHE_SIZE) {
      imageCache.set(cacheKey, {
        data: imageBuffer,
        contentType,
        timestamp: Date.now()
      });
      console.log(`[Cache] 缓存图片: ${imagePath} (${imageBuffer.length} bytes)`);
    }

    // 设置响应头
    res.set({
      'Content-Type': contentType,
      'Content-Length': imageBuffer.length,
      'Cache-Control': 'public, max-age=86400', // 24小时浏览器缓存
      'X-Cache': 'MISS'
    });

    console.log(`[SFTP] 图片下载成功: ${imagePath} (${imageBuffer.length} bytes)`);
    res.send(imageBuffer);

  } catch (error) {
    console.error(`[SFTP] 下载图片失败: ${imagePath}`, error.message);
    
    // 返回错误信息
    res.status(404).json({
      error: 'Image not found',
      message: '图片未找到或下载失败',
      path: imagePath,
      details: error.message
    });
  } finally {
    // 关闭SFTP连接
    try {
      await sftp.end();
      console.log(`[SFTP] 连接已关闭`);
    } catch (closeError) {
      console.error(`[SFTP] 关闭连接时出错:`, closeError.message);
    }
  }
});

// 健康检查路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'SFTP Image Proxy',
    protocol: 'SFTP',
    cache: {
      size: imageCache.size,
      maxSize: MAX_CACHE_SIZE
    },
    timestamp: new Date().toISOString()
  });
});

// 缓存状态路由
app.get('/cache/status', (req, res) => {
  const cacheEntries = Array.from(imageCache.entries()).map(([key, value]) => ({
    path: key,
    size: value.data.length,
    contentType: value.contentType,
    cached: new Date(value.timestamp).toISOString(),
    age: Date.now() - value.timestamp
  }));

  res.json({
    totalEntries: imageCache.size,
    maxSize: MAX_CACHE_SIZE,
    entries: cacheEntries
  });
});

// 清理缓存路由
app.delete('/cache', (req, res) => {
  const sizeBefore = imageCache.size;
  imageCache.clear();
  console.log(`[Cache] 手动清理缓存，清理了 ${sizeBefore} 个条目`);
  
  res.json({
    message: '缓存已清理',
    clearedEntries: sizeBefore
  });
});

// SFTP连接测试路由
app.get('/test-connection', async (req, res) => {
  const sftp = new SftpClient();
  
  try {
    console.log(`[SFTP Test] 测试连接到: ${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`);
    await sftp.connect(SFTP_CONFIG);
    
    // 获取根目录列表
    const list = await sftp.list('./');
    
    await sftp.end();
    
    res.json({
      success: true,
      message: 'SFTP连接测试成功',
      server: `${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`,
      fileCount: list.length,
      files: list.slice(0, 10).map(item => ({
        name: item.name,
        type: item.type,
        size: item.size
      }))
    });
    
  } catch (error) {
    console.error(`[SFTP Test] 连接测试失败:`, error.message);
    res.status(500).json({
      success: false,
      message: 'SFTP连接测试失败',
      error: error.message
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('[Server Error]', error);
  res.status(500).json({
    error: 'Internal server error',
    message: '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🖼️  SFTP图片代理服务器启动成功`);
  console.log(`📡 监听端口: ${PORT}`);
  console.log(`🔗 SFTP服务器: ${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`);
  console.log(`👤 用户名: ${SFTP_CONFIG.username}`);
  console.log(`💾 缓存配置: 最大${MAX_CACHE_SIZE}张图片，${CACHE_DURATION / 1000 / 60 / 60}小时过期`);
  console.log(`🌐 健康检查: http://localhost:${PORT}/health`);
  console.log(`🔧 连接测试: http://localhost:${PORT}/test-connection`);
  console.log(`📊 缓存状态: http://localhost:${PORT}/cache/status`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在优雅关闭服务器...');
  process.exit(0);
});
