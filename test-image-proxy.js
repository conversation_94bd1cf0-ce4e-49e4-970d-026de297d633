#!/usr/bin/env node

/**
 * 测试图片代理功能
 */

const http = require('http');

// 测试图片路径（示例）
const testImagePaths = [
  '/test.jpg',
  '/images/news/test.jpg',
  '/news/2024/test.jpg',
  'test.jpg',
  'images/test.jpg'
];

async function testImageProxy(imagePath) {
  return new Promise((resolve) => {
    const url = `http://localhost:3002/image?path=${encodeURIComponent(imagePath)}`;
    console.log(`\n🔍 测试图片路径: ${imagePath}`);
    console.log(`📡 请求URL: ${url}`);
    
    const req = http.get(url, (res) => {
      console.log(`📊 响应状态: ${res.statusCode}`);
      console.log(`📋 响应头:`, res.headers);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ 图片获取成功，大小: ${data.length} bytes`);
          if (res.headers['content-type']) {
            console.log(`🎯 内容类型: ${res.headers['content-type']}`);
          }
        } else {
          console.log(`❌ 图片获取失败`);
          try {
            const errorData = JSON.parse(data);
            console.log(`📝 错误信息:`, errorData);
          } catch (e) {
            console.log(`📝 原始响应:`, data.substring(0, 200));
          }
        }
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ 请求失败: ${error.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      console.log(`⏰ 请求超时`);
      req.destroy();
      resolve();
    });
  });
}

async function testHealthCheck() {
  console.log('🏥 测试健康检查...');
  
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3002/health', (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const healthData = JSON.parse(data);
            console.log('✅ 代理服务器健康状态:', healthData);
          } catch (e) {
            console.log('⚠️  健康检查响应格式异常:', data);
          }
        } else {
          console.log(`❌ 健康检查失败: ${res.statusCode}`);
        }
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ 健康检查请求失败: ${error.message}`);
      resolve();
    });
  });
}

async function main() {
  console.log('🚀 图片代理测试开始\n');
  
  // 测试健康检查
  await testHealthCheck();
  
  // 测试不同的图片路径
  for (const imagePath of testImagePaths) {
    await testImageProxy(imagePath);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
  }
  
  console.log('\n✨ 测试完成!');
  console.log('\n💡 提示:');
  console.log('- 如果所有路径都返回404，说明FTP服务器上可能没有这些测试图片');
  console.log('- 请检查实际的新闻数据中的jpg_path字段内容');
  console.log('- 可以尝试使用真实的图片路径进行测试');
}

if (require.main === module) {
  main().catch(console.error);
}
