import React, { useState } from 'react';
import styled from 'styled-components';
import { Input, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { CesiumController } from '../controllers/CesiumController';
import { tleDataManager } from '../../../services/tleDataManager';
import * as Cesium from 'cesium';

interface SatelliteSearchPanelProps {
  visible: boolean;
  cesiumController?: CesiumController;
}

const SearchPanelContainer = styled.div<{ $visible: boolean }>`
  position: absolute;
  left: 80px;
  top: 50%;
  transform: translateY(calc(-50% - 240px)); /* 增加与图层面板的间距 */
  width: 220px;
  background: transparent; /* 移除外层背景 */
  backdrop-filter: none; /* 移除外层毛玻璃效果 */
  border: none; /* 移除外层边框 */
  border-radius: 0; /* 移除外层圆角 */
  padding: 0; /* 移除外层内边距 */
  color: white;
  opacity: 1; /* 始终显示，不受visible控制 */
  visibility: visible; /* 始终可见 */
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: none; /* 移除外层阴影 */
`;

const StyledInput = styled(Input)`
  background: rgba(0, 0, 0, 0.4) !important; /* 增强内层背景 */
  backdrop-filter: blur(10px) !important; /* 添加毛玻璃效果 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important; /* 增加圆角 */
  color: white !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important; /* 添加阴影 */
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.8) !important; /* 提高提示词亮度 */
  }
  
  &:hover {
    border-color: rgba(64, 169, 255, 0.6) !important;
    background: rgba(0, 0, 0, 0.5) !important; /* 悬停时加深背景 */
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4) !important; /* 悬停时增强阴影 */
  }
  
  &:focus {
    border-color: #40a9ff !important;
    background: rgba(0, 0, 0, 0.5) !important; /* 聚焦时加深背景 */
    box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2), 0 6px 25px rgba(0, 0, 0, 0.4) !important;
  }
  
  .ant-input {
    background: transparent !important;
    border: none !important;
    color: white !important;
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.8) !important; /* 确保内部input的提示词也是亮色 */
    }
  }
  
  .ant-input-prefix {
    color: rgba(255, 255, 255, 0.8) !important; /* 搜索图标也调亮 */
  }
`;

export const SatelliteSearchPanel: React.FC<SatelliteSearchPanelProps> = ({
  visible,
  cesiumController
}) => {
  const [noradId, setNoradId] = useState('');

  const handleSearch = async () => {
    if (!noradId.trim()) {
      message.warning('请输入NORAD ID');
      return;
    }

    if (!cesiumController) {
      message.error('地图控制器未初始化');
      return;
    }

    try {
      const numericNoradId = parseInt(noradId.trim());
      if (isNaN(numericNoradId)) {
        message.error('请输入有效的NORAD ID数字');
        return;
      }

      console.log(`🔍 搜索卫星 NORAD ID: ${numericNoradId}`);

      // 从TLE数据中查找卫星
      const allTleData = await tleDataManager.getTleData();
      const satellite = allTleData.find(item => item.norad_id === numericNoradId);
      
      if (!satellite) {
        message.warning(`未找到NORAD ID为 ${numericNoradId} 的卫星数据`);
        return;
      }

      console.log('🛰️ 找到卫星:', satellite);

      // 解析TLE数据
      const tleLines = satellite.tle_raw.split('\n');
      let line1 = '', line2 = '';

      for (const line of tleLines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('1 ')) {
          line1 = trimmedLine;
        } else if (trimmedLine.startsWith('2 ')) {
          line2 = trimmedLine;
        }
      }

      if (!line1 || !line2) {
        message.error('TLE数据格式错误，无法解析');
        return;
      }

      console.log(`�� 开始显示单个卫星 - 使用与单击卫星完全相同的升级机制`);

      // 先清除之前显示的卫星
      cesiumController.hideAllSatellites();

      // 使用与单击卫星完全相同的机制：先创建LightSatellite，然后升级为Entity
      const lightSatelliteRenderer = cesiumController.lightSatelliteRenderer;
      
      if (!lightSatelliteRenderer) {
        message.error('轻量级卫星渲染器未初始化');
        return;
      }

      // 计算卫星当前位置
      const satellite_sgp4 = await import('satellite.js');
      const satrec = satellite_sgp4.twoline2satrec(line1, line2);
      
      if (!satrec) {
        message.error('TLE数据解析失败，无法创建卫星轨道模型');
        return;
      }
      
      const now = new Date();
      const positionAndVelocity = satellite_sgp4.propagate(satrec, now);
      
      if (!positionAndVelocity.position || typeof positionAndVelocity.position === 'boolean') {
        message.error('无法计算卫星位置');
        return;
      }

      const positionEci = positionAndVelocity.position as any;
      const position = new Cesium.Cartesian3(
        positionEci.x * 1000, // 转换为米
        positionEci.y * 1000,
        positionEci.z * 1000
      );

      // 准备LightSatelliteData
      const lightSatelliteData = {
        id: satellite.norad_id.toString(),
        name: satellite.satellite_name || `Satellite-${satellite.norad_id}`,
        position: position,
        constellation: satellite.constellation_name || 'Single-Satellite',
        color: Cesium.Color.CYAN,
        tle: {
          line1,
          line2
        }
      };

      // 先添加为Light卫星点
      lightSatelliteRenderer.addBatch([lightSatelliteData]);
      
      // 立即升级为Entity，这将创建与单击卫星完全相同的显示效果（包括轨道路径）
      const promoted = lightSatelliteRenderer.promoteToEntity(satellite.norad_id.toString());

      if (promoted) {
        console.log(`✅ 成功升级卫星为Entity: ${lightSatelliteData.name}`);
        message.success(`成功显示卫星: ${lightSatelliteData.name} (NORAD ID: ${numericNoradId})`);
        
        // 清空输入框
        setNoradId('');
      } else {
        message.error('升级卫星为Entity失败，请重试');
      }
      
    } catch (error) {
      console.error('❌ 搜索卫星失败:', error);
      message.error('搜索卫星失败，请重试');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <SearchPanelContainer $visible={visible}>
      <StyledInput
        placeholder="输入NORAD ID搜索"
        value={noradId}
        onChange={(e) => setNoradId(e.target.value)}
        onKeyPress={handleKeyPress}
        prefix={<SearchOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />}
        size="middle"
      />
    </SearchPanelContainer>
  );
}; 