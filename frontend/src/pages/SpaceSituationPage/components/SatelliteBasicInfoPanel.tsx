import React from 'react';
import styled from 'styled-components';

// 定义API响应的数据结构
export interface SatelliteApiData {
  id: number;
  satellite_name: Array<{value: string, sources: string[]}>;
  alternative_name: Array<{value: string, sources: string[]}>;
  cospar_id: Array<{value: string, sources: string[]}>;
  country_of_registry: Array<{value: string, sources: string[]}>;
  owner: Array<{value: string, sources: string[]}>;
  status: Array<{value: string, sources: string[]}>;
  norad_id: Array<{value: number, sources: string[]}>;
  launch_info: Array<{value: any, sources: string[]}>;
  orbit_info: Array<{value: any, sources: string[]}>;
  purpose: Array<{value: string | string[], sources: string[]}>;
  constellation: Array<{value: string, sources: string[]}>;
  users: Array<{value: string, sources: string[]}>;
}

const Panel = styled.div<{ visible: boolean }>`
  position: fixed;
  right: 20px;
  top: 10%;
  width: 360px;
  max-height: 52vh; /* 原来是70vh，改为2/3，即70 * 2/3 ≈ 46.67vh */
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
  opacity: ${props => props.visible ? 1 : 0};
  visibility: ${props => props.visible ? 'visible' : 'hidden'};
  z-index: 1001;
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  display: flex;
  flex-direction: column;
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
`;

// 新增：可滚动内容容器
const ScrollableContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px 16px;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
`;

const Title = styled.h3`
  margin: 0;
  font-size: 20px;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.5px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 20px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;

  &:hover {
    color: #fff;
  }
`;

const InfoSection = styled.div`
  margin-bottom: 20px;
  
  &:last-of-type {
    margin-bottom: 16px;
  }
  
  &:first-of-type {
    margin-top: 20px;
  }
`;

const SectionTitle = styled.h4`
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #00bcd4;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border-left: 3px solid #00bcd4;
  padding-left: 8px;
`;

const InfoItem = styled.div`
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
`;

const Label = styled.span`
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin-right: 12px;
  min-width: 85px;
  flex-shrink: 0;
  font-weight: 500;
`;

const Value = styled.div`
  color: rgba(255, 255, 255, 0.95);
  font-size: 13px;
  flex: 1;
  word-break: break-word;
  line-height: 1.4;
  font-weight: 400;
`;

const MultiValue = styled.div`
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailsButton = styled.button`
  width: 100%;
  background: linear-gradient(135deg, #00bcd4, #0097a7);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 14px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 600;
  margin-top: 20px;
  letter-spacing: 0.5px;
  text-transform: uppercase;

  &:hover {
    background: linear-gradient(135deg, #0097a7, #00838f);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 188, 212, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  
  &::before {
    content: '';
    width: 32px;
    height: 32px;
    border: 3px solid rgba(0, 188, 212, 0.3);
    border-top: 3px solid #00bcd4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorContainer = styled.div`
  padding: 40px 20px;
  text-align: center;
  color: #ff6b6b;
  font-size: 14px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 107, 107, 0.2);
  margin: 10px 0;
`;

interface SatelliteBasicInfoPanelProps {
  satelliteData: SatelliteApiData | null;
  loading: boolean;
  error: string | null;
  visible: boolean;
  onClose: () => void;
}

export const SatelliteBasicInfoPanel: React.FC<SatelliteBasicInfoPanelProps> = ({
  satelliteData,
  loading,
  error,
  visible,
  onClose,
}) => {
  // 获取第一个有效值的辅助函数
  const getFirstValue = <T,>(items?: Array<{value: T, sources: string[]}>): T | null => {
    if (!items || items.length === 0) return null;
    return items[0].value;
  };

  // 格式化用途信息
  const formatPurpose = (purpose: string | string[] | null): string => {
    if (!purpose) return '未知';
    if (Array.isArray(purpose)) {
      return purpose.join(', ');
    }
    return purpose;
  };

  // 格式化日期
  const formatDate = (dateStr: string | null): string => {
    if (!dateStr) return '未知';
    try {
      return new Date(dateStr).toLocaleDateString('zh-CN');
    } catch {
      return dateStr;
    }
  };

  // 处理查看详情
  const handleViewDetails = () => {
    if (!satelliteData) return;
    
    try {
      // 缓存卫星数据到localStorage
      localStorage.setItem('current_satellite_data', JSON.stringify(satelliteData));
      
      // 获取卫星ID（优先使用COSPAR ID，其次使用NORAD ID）
      const cosparId = getFirstValue(satelliteData.cospar_id);
      const noradId = getFirstValue(satelliteData.norad_id);
      const id = cosparId || noradId || satelliteData.id;
      
      // 打开卫星详情页
      window.open(`/satellite/${id}`, '_blank');
    } catch (error) {
      console.error('打开卫星详情页失败:', error);
    }
  };

  if (!visible) return null;

  if (loading) {
    return (
      <Panel visible={visible}>
        <TitleBar>
          <Title>加载中...</Title>
          <CloseButton onClick={onClose} title="关闭">×</CloseButton>
        </TitleBar>
        <LoadingContainer>
          <div>正在获取卫星信息...</div>
        </LoadingContainer>
      </Panel>
    );
  }

  if (error) {
    return (
      <Panel visible={visible}>
        <TitleBar>
          <Title>错误</Title>
          <CloseButton onClick={onClose} title="关闭">×</CloseButton>
        </TitleBar>
        <ErrorContainer>
          {error}
        </ErrorContainer>
      </Panel>
    );
  }

  if (!satelliteData) {
    return (
      <Panel visible={visible}>
        <TitleBar>
          <Title>未找到数据</Title>
          <CloseButton onClick={onClose} title="关闭">×</CloseButton>
        </TitleBar>
        <ErrorContainer>
          未找到卫星信息
        </ErrorContainer>
      </Panel>
    );
  }

  const satelliteName = getFirstValue(satelliteData.satellite_name) || 
                       getFirstValue(satelliteData.alternative_name) || 
                       `卫星-${satelliteData.id}`;
  
  const noradId = getFirstValue(satelliteData.norad_id);
  const status = getFirstValue(satelliteData.status);
  
  const launchInfo = getFirstValue(satelliteData.launch_info);
  const orbitInfo = getFirstValue(satelliteData.orbit_info);

  return (
    <Panel visible={visible}>
      <TitleBar>
        <Title>{satelliteName}</Title>
        <CloseButton onClick={onClose} title="关闭">×</CloseButton>
      </TitleBar>

      <ScrollableContent>
        {/* 基本信息 */}
        <InfoSection>
          <SectionTitle>基本信息</SectionTitle>
          
          {satelliteData.alternative_name && satelliteData.alternative_name.length > 0 && (
            <InfoItem>
              <Label>别名:</Label>
              <Value>
                {satelliteData.alternative_name.map((item, index) => (
                  <MultiValue key={index}>{item.value}</MultiValue>
                ))}
              </Value>
            </InfoItem>
          )}
          
          <InfoItem>
            <Label>NORAD ID:</Label>
            <Value>{getFirstValue(satelliteData.norad_id) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>COSPAR ID:</Label>
            <Value>{getFirstValue(satelliteData.cospar_id) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>状态:</Label>
            <Value>{getFirstValue(satelliteData.status) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>星座:</Label>
            <Value>{getFirstValue(satelliteData.constellation) || '未知'}</Value>
          </InfoItem>
        </InfoSection>

        {/* 运营信息 */}
        <InfoSection>
          <SectionTitle>运营信息</SectionTitle>
          
          <InfoItem>
            <Label>所有者:</Label>
            <Value>{getFirstValue(satelliteData.owner) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>注册国:</Label>
            <Value>{getFirstValue(satelliteData.country_of_registry) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>用户:</Label>
            <Value>{getFirstValue(satelliteData.users) || '未知'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>用途:</Label>
            <Value>{formatPurpose(getFirstValue(satelliteData.purpose))}</Value>
          </InfoItem>
        </InfoSection>

        {/* 发射信息 */}
        {launchInfo && (
          <InfoSection>
            <SectionTitle>发射信息</SectionTitle>
            
            <InfoItem>
              <Label>发射日期:</Label>
              <Value>{formatDate(launchInfo.launch_date)}</Value>
            </InfoItem>
            
            {launchInfo.launch_site && (
              <InfoItem>
                <Label>发射场:</Label>
                <Value>{launchInfo.launch_site}</Value>
              </InfoItem>
            )}
            
            {launchInfo.launch_vehicle && (
              <InfoItem>
                <Label>运载火箭:</Label>
                <Value>{launchInfo.launch_vehicle}</Value>
              </InfoItem>
            )}
            
            {launchInfo.contractor && (
              <InfoItem>
                <Label>承包商:</Label>
                <Value>{launchInfo.contractor}</Value>
              </InfoItem>
            )}
          </InfoSection>
        )}

        {/* 轨道信息 */}
        {orbitInfo && (
          <InfoSection>
            <SectionTitle>轨道信息</SectionTitle>
            
            {orbitInfo.orbit_class && (
              <InfoItem>
                <Label>轨道类型:</Label>
                <Value>{orbitInfo.orbit_class}</Value>
              </InfoItem>
            )}
            
            {orbitInfo.incl_degrees !== undefined && (
              <InfoItem>
                <Label>轨道倾角:</Label>
                <Value>{orbitInfo.incl_degrees}°</Value>
              </InfoItem>
            )}
            
            {orbitInfo.period_minutes !== undefined && (
              <InfoItem>
                <Label>轨道周期:</Label>
                <Value>{orbitInfo.period_minutes} 分钟</Value>
              </InfoItem>
            )}
            
            {orbitInfo.apogee_km !== undefined && (
              <InfoItem>
                <Label>远地点:</Label>
                <Value>{orbitInfo.apogee_km} km</Value>
              </InfoItem>
            )}
            
            {orbitInfo.perigee_km !== undefined && (
              <InfoItem>
                <Label>近地点:</Label>
                <Value>{orbitInfo.perigee_km} km</Value>
              </InfoItem>
            )}
            
            {orbitInfo.eccentricity !== undefined && (
              <InfoItem>
                <Label>偏心率:</Label>
                <Value>{orbitInfo.eccentricity}</Value>
              </InfoItem>
            )}
          </InfoSection>
        )}

        <DetailsButton onClick={handleViewDetails} title="查看详细信息">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          查看详情
        </DetailsButton>
      </ScrollableContent>
    </Panel>
  );
}; 