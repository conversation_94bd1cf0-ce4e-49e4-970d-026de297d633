import React from 'react';
import { motion } from 'framer-motion';
import { useHotThemes } from '../../hooks/useHotThemes';

interface TagCloudProps {
  activeTags: string[];
  onTagToggle: (tag: string) => void;
  onClear: () => void;
}

export function TagCloud({ activeTags, onTagToggle, onClear }: TagCloudProps) {
  const { tags, isLoading, error } = useHotThemes();

  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-white/10">
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h2 className="text-base sm:text-lg font-medium text-white">热门标签</h2>
      </div>

      {isLoading && (
        <div className="flex justify-center py-4">
          <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {error && (
        <div className="text-red-400 text-xs sm:text-sm py-2">{error}</div>
      )}

      {!isLoading && !error && (
        <div className="flex flex-wrap gap-1.5 sm:gap-2">
          {tags.map(tag => (
            <motion.button
              key={tag.name}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onTagToggle(tag.name)}
              className={`
                px-2.5 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm
                transition-colors duration-200
                ${activeTags.includes(tag.name)
                  ? 'bg-blue-500 text-white'
                  : 'bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white'
                }
              `}
            >
              {tag.name}
              <span className="ml-1 sm:ml-1.5 text-xs opacity-60">
                {tag.count}
              </span>
            </motion.button>
          ))}
        </div>
      )}
    </div>
  );
}