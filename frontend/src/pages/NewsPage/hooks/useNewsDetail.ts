import { useState, useEffect } from 'react';
import { NewsItem } from '../types';
import { mockNews } from '../data/mockNews';
import { newsService, ESNewsItem } from '../../../services/newsService';
import { useSearchParams } from 'react-router-dom';
import { formatNewsDateTime } from '../../../utils/dateUtils';

export function useNewsDetail(id: string) {
  const [news, setNews] = useState<NewsItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  // 初始化为不显示原文（即默认显示中文）
  const [showOriginal, setShowOriginal] = useState(false);

  // 添加调试日志
  console.log('[useNewsDetail] Initial state:', { showOriginal });
  // 获取URL参数，判断是否来自军事新闻页面
  const [searchParams] = useSearchParams();
  const fromType = searchParams.get('from') || 'latest';
  const rocketName = searchParams.get('rocketName');

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setIsLoading(true);
        console.log('[useNewsDetail] Fetching news with id:', id, 'from type:', fromType, 'rocket name:', rocketName);

        // 优化查找：直接使用较大限制一次性获取
        console.log(`[useNewsDetail] Fetching news with single request`);
        
        let response;
        let foundNewsItem: ESNewsItem | null = null;

        // 根据来源类型选择不同的API调用，使用合理的限制
        if (fromType === 'military') {
          response = await newsService.getMilitaryNewsList({
            page: 1,
            limit: 50, // 一次性获取50条军事新闻
            indexPatterns: [
              "*defence*",
              "*defense*"
            ]
          });
        } else if (fromType === 'rocket' && rocketName) {
          response = await newsService.getNewsList({
            page: 1,
            limit: 30, // 一次性获取30条相关新闻
            keywords: [rocketName],
            keywordMatchType: 'substring'
          });
        } else {
          response = await newsService.getNewsList({
            page: 1,
            limit: 50 // 一次性获取50条最新新闻
          });
        }

        if (response.success && response.data) {
          const newsData = response.data.data || response.data.hits || [];
          console.log(`[useNewsDetail] Got ${newsData.length} news items`);
          
          // 查找匹配的ID
          foundNewsItem = newsData.find((item: ESNewsItem) => {
            const itemId = String(item._id);
            const targetId = String(id);
            return itemId === targetId;
          }) || null;

          if (foundNewsItem) {
            console.log(`[useNewsDetail] Found news item:`, foundNewsItem._id);
          } else {
            console.log(`[useNewsDetail] News item not found in current batch`);
          }
        }

        if (foundNewsItem) {
          // 格式化发布日期，包含时分秒
          const utcDate = new Date(foundNewsItem.publish_date.year, foundNewsItem.publish_date.month - 1, foundNewsItem.publish_date.day, foundNewsItem.publish_date.hour, foundNewsItem.publish_date.minute, foundNewsItem.publish_date.second);
          const publishDate = formatNewsDateTime(utcDate.toISOString());

          // 处理themes_cn字段，确保它是一个有效的数组
          let themesData: string[] = [];
          if (foundNewsItem.themes_cn && Array.isArray(foundNewsItem.themes_cn)) {
            themesData = foundNewsItem.themes_cn;
          } else if (typeof foundNewsItem.themes_cn === 'string') {
            try {
              const parsed = JSON.parse(foundNewsItem.themes_cn);
              if (Array.isArray(parsed)) {
                themesData = parsed;
              }
            } catch (e) {
              if (foundNewsItem.themes_cn) {
                themesData = [foundNewsItem.themes_cn];
              }
            }
          }

          // 创建新闻项
          const newsItem: NewsItem = {
            id: foundNewsItem._id,
            title: foundNewsItem.title_cn || foundNewsItem.title,
            title_cn: foundNewsItem.title_cn,
            title_en: foundNewsItem.title,
            summary: foundNewsItem.summary_cn || foundNewsItem.summary,
            summary_cn: foundNewsItem.summary_cn,
            summary_en: foundNewsItem.summary,
            content: foundNewsItem.content || '',
            content_cn: foundNewsItem.content_cn || '',
            date: publishDate,
            imageUrl: foundNewsItem.thumbnail_url,
            tags: themesData,
            themes_cn: themesData,
            isRead: true,
            matchedKeywords: [],
            author: {
              name: foundNewsItem.author || '未知作者',
              avatar: undefined
            },
            source: foundNewsItem.source || '',
            originalData: foundNewsItem
          };

          // 设置新闻数据
          setNews(newsItem);
          return;
        }

        // 如果从服务器获取失败，尝试使用模拟数据
        const foundNews = mockNews.find(item => item.id === id);
        if (foundNews) {
          setNews(foundNews);
        } else {
          setNews(null);
        }
      } catch (error) {
        console.error('Failed to fetch news detail:', error);
        setNews(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNews();
  }, [id, fromType, rocketName]);

  const toggleOriginal = () => {
    setShowOriginal(prev => !prev);
  };

  return {
    news,
    isLoading,
    showOriginal,
    toggleOriginal
  };
}