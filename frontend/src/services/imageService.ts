/**
 * 图片服务
 * 处理FTP图片代理和图片URL生成
 */

// 图片代理服务器配置
const IMAGE_PROXY_BASE_URL = 'http://localhost:3002';

// 默认太空图片
export const DEFAULT_SPACE_IMAGE = 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80';

/**
 * 图片URL配置接口
 */
export interface ImageUrlOptions {
  /** 是否启用代理服务 */
  useProxy?: boolean;
  /** 图片质量参数 */
  quality?: number;
  /** 图片尺寸参数 */
  size?: string;
}

/**
 * 从jpg_path数组生成图片URL
 * @param jpgPaths - 图片路径数组
 * @param options - 配置选项
 * @returns 图片URL，如果没有有效路径则返回默认图片
 */
export function getImageUrl(jpgPaths: string[] | string | undefined, options: ImageUrlOptions = {}): string {
  const { useProxy = true } = options;

  // 处理输入参数
  let paths: string[] = [];
  
  if (typeof jpgPaths === 'string') {
    paths = [jpgPaths];
  } else if (Array.isArray(jpgPaths)) {
    paths = jpgPaths;
  }

  // 过滤有效路径
  const validPaths = paths.filter(path => 
    path && 
    typeof path === 'string' && 
    path.trim().length > 0 &&
    !path.includes('undefined') &&
    !path.includes('null')
  );

  if (validPaths.length === 0) {
    console.log('[ImageService] 没有有效的图片路径，使用默认图片');
    return DEFAULT_SPACE_IMAGE;
  }

  // 使用第一个有效路径
  const imagePath = validPaths[0].trim();
  
  if (!useProxy) {
    // 直接返回原始路径（用于测试或特殊情况）
    return imagePath;
  }

  // 生成代理URL
  const proxyUrl = `${IMAGE_PROXY_BASE_URL}/image?path=${encodeURIComponent(imagePath)}`;
  
  console.log(`[ImageService] 生成代理URL: ${imagePath} -> ${proxyUrl}`);
  return proxyUrl;
}

/**
 * 从jpg_path数组获取所有图片URL
 * @param jpgPaths - 图片路径数组
 * @param options - 配置选项
 * @returns 图片URL数组
 */
export function getAllImageUrls(jpgPaths: string[] | string | undefined, options: ImageUrlOptions = {}): string[] {
  const { useProxy = true } = options;

  // 处理输入参数
  let paths: string[] = [];
  
  if (typeof jpgPaths === 'string') {
    paths = [jpgPaths];
  } else if (Array.isArray(jpgPaths)) {
    paths = jpgPaths;
  }

  // 过滤有效路径
  const validPaths = paths.filter(path => 
    path && 
    typeof path === 'string' && 
    path.trim().length > 0 &&
    !path.includes('undefined') &&
    !path.includes('null')
  );

  if (validPaths.length === 0) {
    return [DEFAULT_SPACE_IMAGE];
  }

  if (!useProxy) {
    return validPaths;
  }

  // 生成所有代理URL
  return validPaths.map(imagePath => {
    const proxyUrl = `${IMAGE_PROXY_BASE_URL}/image?path=${encodeURIComponent(imagePath.trim())}`;
    return proxyUrl;
  });
}

/**
 * 检查图片代理服务是否可用
 * @returns Promise<boolean>
 */
export async function checkImageProxyHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${IMAGE_PROXY_BASE_URL}/health`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('[ImageService] 代理服务健康检查通过:', data);
      return true;
    } else {
      console.warn('[ImageService] 代理服务健康检查失败:', response.status);
      return false;
    }
  } catch (error) {
    console.warn('[ImageService] 代理服务不可用:', error);
    return false;
  }
}

/**
 * 预加载图片
 * @param imageUrl - 图片URL
 * @returns Promise<boolean> - 是否加载成功
 */
export function preloadImage(imageUrl: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    
    img.onload = () => {
      console.log(`[ImageService] 图片预加载成功: ${imageUrl}`);
      resolve(true);
    };
    
    img.onerror = (error) => {
      console.warn(`[ImageService] 图片预加载失败: ${imageUrl}`, error);
      resolve(false);
    };
    
    img.src = imageUrl;
  });
}

/**
 * 批量预加载图片
 * @param imageUrls - 图片URL数组
 * @returns Promise<boolean[]> - 每个图片的加载结果
 */
export async function preloadImages(imageUrls: string[]): Promise<boolean[]> {
  const promises = imageUrls.map(url => preloadImage(url));
  return Promise.all(promises);
}

/**
 * 图片加载错误处理函数
 * @param event - 错误事件
 * @param fallbackUrl - 备用图片URL
 */
export function handleImageError(event: React.SyntheticEvent<HTMLImageElement>, fallbackUrl: string = DEFAULT_SPACE_IMAGE): void {
  const img = event.currentTarget;
  
  // 避免无限循环
  if (img.src === fallbackUrl) {
    console.error('[ImageService] 备用图片也加载失败');
    return;
  }
  
  console.warn(`[ImageService] 图片加载失败，使用备用图片: ${img.src} -> ${fallbackUrl}`);
  img.src = fallbackUrl;
}

/**
 * 创建带有错误处理的图片加载函数
 * @param jpgPaths - 图片路径
 * @param options - 配置选项
 * @returns 包含URL和错误处理函数的对象
 */
export function createImageLoader(jpgPaths: string[] | string | undefined, options: ImageUrlOptions = {}) {
  const imageUrl = getImageUrl(jpgPaths, options);
  
  const onError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    handleImageError(event, DEFAULT_SPACE_IMAGE);
  };
  
  return {
    imageUrl,
    onError,
    fallbackUrl: DEFAULT_SPACE_IMAGE
  };
}

/**
 * 图片服务配置
 */
export const imageServiceConfig = {
  proxyBaseUrl: IMAGE_PROXY_BASE_URL,
  defaultImage: DEFAULT_SPACE_IMAGE,
  cacheEnabled: true,
  retryAttempts: 3,
  timeout: 10000
};

/**
 * 更新图片服务配置
 * @param config - 新的配置
 */
export function updateImageServiceConfig(config: Partial<typeof imageServiceConfig>): void {
  Object.assign(imageServiceConfig, config);
  console.log('[ImageService] 配置已更新:', imageServiceConfig);
}
