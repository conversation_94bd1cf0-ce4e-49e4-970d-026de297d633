import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 100;
`;

const IconButton = styled.button`
  width: 40px;
  height: 40px;
  background: linear-gradient(
    180deg, 
    rgb(40, 40, 40) 0%,
    rgb(20, 20, 20) 40%,
    rgb(0, 0, 0) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.4),
    inset 0 1px 1px rgba(255, 255, 255, 0.2),
    inset 0 -2px 2px rgba(0, 0, 0, 0.4);

  &:hover {
    background: linear-gradient(
      180deg, 
      rgb(50, 50, 50) 0%,
      rgb(30, 30, 30) 40%,
      rgb(10, 10, 10) 100%
    );
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.5),
      inset 0 1px 2px rgba(255, 255, 255, 0.3),
      inset 0 -2px 3px rgba(0, 0, 0, 0.5);
  }

  &:active {
    background: linear-gradient(
      180deg, 
      rgb(15, 15, 15) 0%,
      rgb(25, 25, 25) 40%,
      rgb(0, 0, 0) 100%
    );
    transform: translateY(0);
    box-shadow: 
      0 1px 2px rgba(0, 0, 0, 0.4),
      inset 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  svg {
    width: 20px;
    height: 20px;
  }

  &.active {
    background: linear-gradient(
      180deg, 
      rgb(50, 50, 50) 0%,
      rgb(30, 30, 30) 40%,
      rgb(10, 10, 10) 100%
    );
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.5),
      inset 0 1px 2px rgba(255, 255, 255, 0.3),
      inset 0 -2px 3px rgba(0, 0, 0, 0.5);
  }
`;

// 自定义图层图标组件
const LayersIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 2L3 5.5L10 9L17 5.5L10 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3 9.5L10 13L17 9.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3 13.5L10 17L17 13.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

interface SideButtonsProps {
  onLayersHover?: (isHovered: boolean) => void;
  layersPanelVisible?: boolean;
}

export function SideButtons({ 
  onLayersHover, 
  layersPanelVisible = false
}: SideButtonsProps) {
  const handleLayersMouseEnter = () => {
    if (onLayersHover) {
      onLayersHover(true);
    }
  };

  const handleLayersMouseLeave = () => {
    if (onLayersHover) {
      onLayersHover(false);
    }
  };

  return (
    <>
      <Container>
        <IconButton 
          onMouseEnter={handleLayersMouseEnter}
          onMouseLeave={handleLayersMouseLeave}
          title="图层"
          className={layersPanelVisible ? 'active' : ''}
        >
          <LayersIcon />
        </IconButton>
      </Container>
    </>
  );
} 