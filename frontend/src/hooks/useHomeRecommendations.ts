import { useState, useEffect } from 'react';
import api from '../services/api';
import { newsService } from '../services/newsService';
import { formatNewsDateTime } from '../utils/dateUtils';

/**
 * 推荐项接口
 */
export interface RecommendationItem {
  id: string;
  title: string;
  description: string;
  tag: string;
  priority: 'high' | 'medium' | 'low';
  type: 'launch' | 'news';
  date?: string;
  url?: string;
}

/**
 * 发射数据接口
 */
interface LaunchItem {
  _id: string;
  launch_date: string;
  rocket_name: string;
  mission_name: string;
  launch_description: string;
  site_name: string;
  provider: string;
  status: string;
}

/**
 * 新闻数据接口
 */
interface NewsItem {
  _id: string;
  title: string;
  title_cn?: string;
  summary: string;
  summary_cn?: string;
  publish_date: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    second: number;
  };
  source: string;
}



/**
 * 获取首页推荐数据的Hook
 */
export function useHomeRecommendations() {
  const [recommendations, setRecommendations] = useState<RecommendationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取日期字符串（YYYY-MM-DD格式）
   */
  const getDateString = (daysOffset: number = 0): string => {
    const date = new Date();
    date.setDate(date.getDate() + daysOffset);
    return date.toISOString().split('T')[0];
  };

  /**
   * 获取发射信息（最近一次即将发射任务 + 最近一次历史发射任务）
   */
  const fetchLaunchInfo = async (): Promise<RecommendationItem[]> => {
    try {
      // 获取最近一次历史发射任务（历史发射，按时间倒序）
      const historicalResponse = await api.post('/api/es/launch/search', {
        page: 1,
        limit: 1, // 只获取最近一次历史发射任务
        sortDirection: 'desc', // 按时间倒序排列
        status: '历史发射'
      });

      // 获取最近一次即将发射任务（即将发射，按时间正序，过滤掉过去的时间）
      const now = new Date();
      const currentDateString = now.toISOString().split('T')[0]; // 获取当前日期 YYYY-MM-DD 格式
      
      const upcomingResponse = await api.post('/api/es/launch/search', {
        page: 1,
        limit: 1, // 只获取最近一次即将发射任务
        sortDirection: 'asc', // 按时间正序排列
        status: '即将发射',
        launchDateStart: currentDateString // 只获取当前时间之后的发射任务
      });

      const recommendations: RecommendationItem[] = [];

      // 处理最近一次历史发射任务
      if (historicalResponse.data && historicalResponse.data.success && 
          historicalResponse.data.data.results && historicalResponse.data.data.results.length > 0) {
        const launch = historicalResponse.data.data.results[0];
        recommendations.push({
          id: `launch-historical-${launch._id}`,
          title: `${launch.rocket_name} 发射任务`,
          description: `${launch.mission_name} - ${launch.site_name}`,
          tag: '历史发射',
          priority: 'medium' as const,
          type: 'launch' as const,
          date: formatNewsDateTime(launch.launch_date),
          url: `/launches?tab=historical`
        });
      }

      // 处理最近一次即将发射任务
      if (upcomingResponse.data && upcomingResponse.data.success && 
          upcomingResponse.data.data.results && upcomingResponse.data.data.results.length > 0) {
        const launch = upcomingResponse.data.data.results[0];
        recommendations.push({
          id: `launch-upcoming-${launch._id}`,
          title: `${launch.rocket_name} 发射任务`,
          description: `${launch.mission_name} - ${launch.site_name}`,
          tag: '即将发射',
          priority: 'high' as const,
          type: 'launch' as const,
          date: formatNewsDateTime(launch.launch_date),
          url: `/launches?tab=upcoming`
        });
      }

      return recommendations;
    } catch (error) {
      console.error('获取发射数据失败:', error);
      return [];
    }
  };

  /**
   * 获取新闻信息（最新3条新闻）
   */
  const fetchNewsInfo = async (): Promise<RecommendationItem[]> => {
    try {
      console.log('开始获取新闻推荐数据...');
      const response = await newsService.getNewsList({
        page: 1,
        limit: 3, // 获取3条最新新闻
      });

      console.log('新闻API响应:', response);

      if (response.success) {
        // 检查新旧API版本的数据字段
        const newsData = response.data.hits || response.data.data || [];
        console.log('获取到的新闻数据:', newsData);
        
        if (newsData.length > 0) {
          // API 返回的新闻已经按时间倒序排序
          const newsRecommendations = newsData.map((news: NewsItem) => ({
            id: `news-${news._id}`,
            title: news.title_cn || news.title,
            description: news.summary_cn || news.summary,
            tag: '新闻',
            priority: 'medium' as const,
            type: 'news' as const,
            date: formatNewsDateTime(new Date(news.publish_date.year, news.publish_date.month - 1, news.publish_date.day, news.publish_date.hour, news.publish_date.minute, news.publish_date.second).toISOString()),
            url: `/news/${news._id}`
          }));
          console.log('转换后的新闻推荐:', newsRecommendations);
          return newsRecommendations;
        } else {
          console.log('没有获取到新闻数据');
        }
      } else {
        console.log('新闻API返回失败:', response);
      }
      return [];
    } catch (error) {
      console.error('获取新闻数据失败:', error);
      return [];
    }
  };

  /**
   * 获取所有推荐数据
   */
  const fetchRecommendations = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('开始获取推荐数据...');
      const [launches, news] = await Promise.all([
        fetchLaunchInfo(),
        fetchNewsInfo()
      ]);

      console.log('获取到的发射数据:', launches);
      console.log('获取到的新闻数据:', news);

      // 合并发射和新闻数据
      const allRecommendations = [...launches, ...news];
      console.log('合并后的推荐数据:', allRecommendations);

      // 如果没有获取到任何数据，提供一些默认推荐
      if (allRecommendations.length === 0) {
        const defaultRecommendations: RecommendationItem[] = [
          {
            id: 'default-1',
            title: '空间态势感知',
            description: '实时监控空间目标动态',
            tag: '系统',
            priority: 'medium',
            type: 'launch',
            url: '/space-situation'
          },
          {
            id: 'default-2',
            title: '卫星数据分析',
            description: '查看最新的卫星轨道信息',
            tag: '分析',
            priority: 'low',
            type: 'news',
            url: '/analysis'
          }
        ];
        setRecommendations(defaultRecommendations);
      } else {
        setRecommendations(allRecommendations);
      }
    } catch (err) {
      console.error('获取推荐数据失败:', err);
      setError(err instanceof Error ? err.message : '获取推荐数据失败');
      
      // 设置默认推荐数据
      const defaultRecommendations: RecommendationItem[] = [
        {
          id: 'error-1',
          title: '系统维护中',
          description: '推荐功能暂时不可用',
          tag: '系统',
          priority: 'low',
          type: 'launch'
        }
      ];
      setRecommendations(defaultRecommendations);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, []);

  return {
    recommendations,
    loading,
    error,
    refetch: fetchRecommendations
  };
} 