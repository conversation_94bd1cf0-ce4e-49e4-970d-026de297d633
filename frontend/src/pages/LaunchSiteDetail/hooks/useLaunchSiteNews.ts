import { useState, useEffect, useCallback } from 'react';
import { newsService, ESNewsItem } from '../../../services/newsService';
import { findLaunchSiteByName } from '../../../data/launchSites';
import { formatNewsDateTime } from '../../../utils/dateUtils';
import { getImageUrl } from '../../../services/imageService';

interface NewsItem {
  id: string;
  title: string;
  date: string;
  summary: string;
  imageUrl: string;
  tags: string[];
  themes_cn: string[];
  author: {
    name: string;
  };
  source: string;
  originalData: any;
}

export function useLaunchSiteNews(siteName: string) {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 10;

  // 获取发射场的所有可能名称（英文名、中文名、别名）
  const getAllLaunchSiteNames = useCallback((name: string): string[] => {
    const launchSite = findLaunchSiteByName(name);
    
    if (!launchSite) {
      return [name]; // 如果找不到发射场信息，就使用传入的名称
    }
    
    // 收集所有可能的名称
    const names: string[] = [
      launchSite.englishName,
      launchSite.chineseName
    ];
    
    // 添加别名
    if (launchSite.aliases && launchSite.aliases.length > 0) {
      names.push(...launchSite.aliases);
    }
    
    // 添加短名称（如Cape Canaveral）
    const shortNames = new Set<string>();
    names.forEach(n => {
      // 匹配英文名称中的主要部分
      const parts = n.split(' ');
      if (parts.length >= 2) {
        if (!n.toLowerCase().includes('launch') && !n.toLowerCase().includes('center') && !n.toLowerCase().includes('site')) {
          shortNames.add(parts.slice(0, 2).join(' '));
        }
      }
    });
    
    return [...names, ...shortNames];
  }, []);

  const fetchNews = useCallback(async (currentPage: number) => {
    try {
      setIsLoading(true);
      
      const searchNames = getAllLaunchSiteNames(siteName);
      console.log(`[useLaunchSiteNews] 搜索发射场名称: `, searchNames);
      
      if (searchNames.length === 0) {
        console.warn(`无法找到发射场 "${siteName}" 的名称信息`);
        setNews([]);
        setHasMore(false);
        setTotalItems(0);
        return;
      }
      
      // 构建请求参数
      const requestParams = {
        page: currentPage,
        limit: pageSize,
        keywords: searchNames,
        keywordMatchType: 'substring' as const
      };
      
      console.log(`[useLaunchSiteNews] 获取发射场相关新闻: ${siteName}, 页码: ${currentPage}`);
      const response = await newsService.getNewsList(requestParams);
      
      if (response.success && response.data.hits) {
        // 保存总数，用于判断是否还有更多
        setTotalItems(response.data.total);
        
        // 将API返回的数据转换为应用所需的格式
        const apiNews = response.data.hits.map((item: ESNewsItem) => {
          // 格式化发布日期，包含时分秒
          const utcDate = new Date(item.publish_date.year, item.publish_date.month - 1, item.publish_date.day, item.publish_date.hour, item.publish_date.minute, item.publish_date.second);
          const publishDate = formatNewsDateTime(utcDate.toISOString());
          
          // 处理themes_cn字段，确保它是一个有效的数组
          let themesData: string[] = [];
          if (item.themes_cn && Array.isArray(item.themes_cn)) {
            themesData = item.themes_cn;
          } else if (typeof item.themes_cn === 'string') {
            try {
              const parsed = JSON.parse(item.themes_cn);
              if (Array.isArray(parsed)) {
                themesData = parsed;
              }
            } catch (e) {
              if (item.themes_cn) {
                themesData = [item.themes_cn];
              }
            }
          }
          
          return {
            id: item._id,
            title: item.title_cn || item.title,
            date: publishDate,
            summary: item.summary_cn || item.summary,
            imageUrl: getImageUrl(item.jpg_path) || item.thumbnail_url || '',
            tags: themesData,
            themes_cn: themesData,
            author: {
              name: item.author || '未知作者'
            },
            source: item.source,
            originalData: item // 保存原始数据以便详情页使用
          };
        });
        
        if (currentPage === 1) {
          // 第一页，直接设置新闻列表
          setNews(apiNews);
        } else {
          // 不是第一页，追加到现有列表，并去重
          setNews(prevNews => {
            // 合并新旧新闻列表并去重
            const allNews = [...prevNews, ...apiNews];
            const uniqueNewsMap = new Map();
            
            allNews.forEach(item => {
              uniqueNewsMap.set(item.id, item);
            });
            
            return Array.from(uniqueNewsMap.values());
          });
        }
        
        // 判断是否还有更多新闻
        setHasMore(response.data.total > currentPage * pageSize);
      } else {
        console.error('获取发射场相关新闻失败:', response);
      }
    } catch (error) {
      console.error('获取发射场相关新闻出错:', error);
    } finally {
      setIsLoading(false);
    }
  }, [siteName, pageSize, getAllLaunchSiteNames]);
  
  // 初始加载
  useEffect(() => {
    if (siteName) {
      setPage(1);
      fetchNews(1);
    }
  }, [fetchNews, siteName]);
  
  // 加载更多
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNews(nextPage);
    }
  }, [fetchNews, hasMore, isLoading, page]);
  
  return {
    news,
    isLoading,
    hasMore,
    loadMore,
    totalItems
  };
} 