import React, { useState, useEffect, KeyboardEvent } from 'react';
import { Search, X } from 'lucide-react';

interface NewsSearchProps {
  onSearch: (query: string) => void;
}

export function NewsSearch({ onSearch }: NewsSearchProps) {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    // 按下回车键时提交搜索
    if (e.key === 'Enter') {
      if (query.trim()) {
        onSearch(query.trim());
      }
    }
  };

  const handleClear = () => {
    setQuery('');
    onSearch(''); // 清空搜索结果
  };

  return (
    <form onSubmit={handleSubmit} className="mb-4 sm:mb-6">
      <div className="
        relative flex items-center
        backdrop-blur-sm bg-white/[0.02]
        rounded-lg
        border border-white/[0.05] hover:border-white/[0.1]
        transition-all duration-200
        group
      ">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="搜索新闻..."
          className="
            w-full h-10 sm:h-12
            px-3 sm:px-4 pr-10 sm:pr-12
            text-sm sm:text-base text-white
            placeholder:text-gray-500
            bg-transparent
            focus:outline-none
          "
        />
        {query ? (
          <button
            type="button"
            onClick={handleClear}
            className="
              absolute right-8 sm:right-10
              w-6 h-6 sm:w-8 sm:h-8
              flex items-center justify-center
              text-gray-400 hover:text-red-400
              transition-colors
            "
          >
            <X className="w-3 h-3 sm:w-4 sm:h-4" />
          </button>
        ) : null}
        <button
          type="submit"
          className="
            absolute right-1 sm:right-2
            w-6 h-6 sm:w-8 sm:h-8
            flex items-center justify-center
            text-gray-400 hover:text-blue-400
            transition-colors
          "
        >
          <Search className="w-3 h-3 sm:w-4 sm:h-4" />
        </button>
      </div>
    </form>
  );
}