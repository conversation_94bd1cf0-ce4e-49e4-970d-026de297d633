import React from 'react';

interface NewsTypeToggleProps {
  activeType: 'latest' | 'military';
  onToggle: (type: 'latest' | 'military') => void;
}

export function NewsTypeToggle({ activeType, onToggle }: NewsTypeToggleProps) {
  return (
    <div className="flex gap-4 sm:gap-6 mb-4 sm:mb-6">
      <button
        onClick={() => onToggle('latest')}
        className={`
          text-sm sm:text-base font-medium
          transition-colors duration-200
          ${activeType === 'latest'
            ? 'text-blue-400'
            : 'text-white/70 hover:text-white'}
        `}
      >
        最新新闻
      </button>
      <button
        onClick={() => onToggle('military')}
        className={`
          text-sm sm:text-base font-medium
          transition-colors duration-200
          ${activeType === 'military'
            ? 'text-blue-400'
            : 'text-white/70 hover:text-white'}
        `}
      >
        军事新闻
      </button>
    </div>
  );
}