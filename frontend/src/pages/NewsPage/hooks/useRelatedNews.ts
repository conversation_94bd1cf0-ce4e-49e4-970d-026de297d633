import { useState, useEffect } from 'react';
import { NewsItem } from '../types';
import { mockNews } from '../data/mockNews';
import { newsService, ESNewsItem } from '../../../services/newsService';
import { formatNewsDateTime } from '../../../utils/dateUtils';

export function useRelatedNews(currentNewsId: string, tags: string[] = []) {
  const [relatedNews, setRelatedNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRelatedNews = async () => {
      try {
        setIsLoading(true);

        try {
          // 尝试从API获取新闻数据
          const response = await newsService.getNewsList({
            page: 1,
            limit: 20 // 获取足够多的新闻来找到相关新闻
          });

          if (response.success && response.data.hits) {
            // 将API返回的数据转换为应用所需的格式
            const apiNews = response.data.hits.map((item: ESNewsItem) => {
              // 格式化发布日期，包含时分秒
              const utcDate = new Date(item.publish_date.year, item.publish_date.month - 1, item.publish_date.day, item.publish_date.hour, item.publish_date.minute, item.publish_date.second);
              const publishDate = formatNewsDateTime(utcDate.toISOString());

              // 生成唯一ID
              const id = item._id;

              // 使用中文标题和摘要（如果有），否则使用英文
              const title = item.title_cn || item.title;
              const summary = item.summary_cn || item.summary;

              // 创建新闻项
              return {
                id,
                title,
                summary,
                content: item.content,
                content_cn: item.content_cn,
                title_cn: item.title_cn,
                title_en: item.title,
                summary_cn: item.summary_cn,
                summary_en: item.summary,
                date: publishDate,
                imageUrl: item.thumbnail_url,
                tags: item.themes_cn || [],
                isRead: false,
                matchedKeywords: [] as string[],
                author: {
                  name: item.author || '未知作者',
                  avatar: undefined
                },
                source: item.source,
                originalData: item
              };
            });

            // 找到相关新闻
            const related = apiNews
              .filter(news =>
                news.id !== currentNewsId &&
                Array.isArray(news.tags) && news.tags.length > 0 &&
                Array.isArray(tags) && tags.length > 0 &&
                news.tags.some(tag => tags.includes(tag))
              )
              .slice(0, 2); // 限制为2条相关新闻

            if (related.length > 0) {
              setRelatedNews(related);
              return; // 找到了相关新闻，不需要继续使用模拟数据
            }
          }
        } catch (apiError) {
          console.error('Failed to fetch related news from API:', apiError);
          // 如果API请求失败，将继续尝试使用模拟数据
        }

        // 如果从API获取失败或没有找到相关新闻，使用模拟数据
        const related = mockNews
          .filter(news =>
            news.id !== currentNewsId &&
            Array.isArray(news.tags) && news.tags.length > 0 &&
            Array.isArray(tags) && tags.length > 0 &&
            news.tags.some(tag => tags.includes(tag))
          )
          .slice(0, 2); // 限制为2条相关新闻

        setRelatedNews(related);
      } catch (error) {
        console.error('Failed to fetch related news:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (Array.isArray(tags) && tags.length > 0) {
      fetchRelatedNews();
    }
  }, [currentNewsId, tags]);

  return {
    relatedNews,
    isLoading
  };
}