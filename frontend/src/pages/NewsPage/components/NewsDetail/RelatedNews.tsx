import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import type { NewsItem } from '../../types';

interface RelatedNewsProps {
  news: NewsItem[];
}

export function RelatedNews({ news }: RelatedNewsProps) {
  return (
    <section className="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-white/10">
      <h2 className="text-lg sm:text-xl font-medium text-white mb-4 sm:mb-6">相关推荐</h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        {news.map((item, index) => (
          <motion.article
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="group"
          >
            <Link to={`/news/${item.id}`} className="block">
              {item.imageUrl && (
                <div className="aspect-video rounded-lg overflow-hidden mb-2 sm:mb-3">
                  <img
                    src={item.imageUrl}
                    alt={item.title}
                    className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}
              
              <h3 className="text-base sm:text-lg font-medium text-white group-hover:text-blue-400 transition-colors line-clamp-2 mb-2 leading-tight">
                {item.title}
              </h3>
              
              <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-400">
                <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>{item.date}</span>
              </div>
            </Link>
          </motion.article>
        ))}
      </div>
    </section>
  );
}