import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { NewsFeed } from './feed/NewsFeed';
import { TagCloud } from './sidebar/TagCloud';
import { NewsSearch } from './sidebar/NewsSearch';
import { useNewsFilters } from '../hooks/useNewsFilters';
import { NewsTypeToggle } from './NewsTypeToggle';
import styled from 'styled-components';
import { DebugPanel } from '../../../components/DebugPanel';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (min-width: 640px) {
    gap: 20px;
  }

  @media (min-width: 1024px) {
    flex-direction: row;
    gap: 24px;
  }
`;

const MainContent = styled.div`
  flex: 1;
  min-width: 0;
`;

const Sidebar = styled.div`
  width: 100%;

  @media (min-width: 1024px) {
    width: 320px;
    flex-shrink: 0;
  }
`;

const StickyContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (min-width: 640px) {
    gap: 20px;
  }

  @media (min-width: 1024px) {
    position: sticky;
    top: 24px;
    gap: 24px;
  }
`;

export function NewsLayout() {
  const { activeTags, toggleTag, clearTags } = useNewsFilters();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // 从 URL 参数获取初始新闻类型
  const [newsType, setNewsType] = useState<'latest' | 'military'>(
    () => {
      const typeFromUrl = searchParams.get('type') as 'latest' | 'following' | 'military';
      // 如果是 'following' 类型，则默认使用 'latest'
      return (typeFromUrl === 'following' ? 'latest' : typeFromUrl) || 'latest';
    }
  );
  const [searchQuery, setSearchQuery] = useState('');

  // 监听 URL 参数变化，更新新闻类型
  useEffect(() => {
    const typeFromUrl = searchParams.get('type') as 'latest' | 'following' | 'military';
    if (typeFromUrl && typeFromUrl !== newsType) {
      // 如果URL中是 'following'，则设置为 'latest'
      const validType = typeFromUrl === 'following' ? 'latest' : typeFromUrl;
      if (validType === 'latest' || validType === 'military') {
        setNewsType(validType);
      }
    }
  }, [searchParams, newsType]);

  // 当新闻类型改变时更新 URL，保留其他参数
  const handleNewsTypeChange = (type: 'latest' | 'military') => {
    setNewsType(type);

    // 保留现有参数，只更新 type 参数
    const newParams = new URLSearchParams(searchParams);
    newParams.set('type', type);
    setSearchParams(newParams);
  };

  const handleSearch = (query: string) => {
    console.log(`[NewsLayout] Search query: "${query}"`);
    setSearchQuery(query);

    // 注意：我们不再清除标签，因为搜索和标签筛选可以同时使用
    // API请求会同时包含keywords和themes参数
  };

  return (
    <>
      <LayoutContainer>
        <MainContent>
          <NewsTypeToggle
            activeType={newsType}
            onToggle={handleNewsTypeChange}
          />
          <NewsFeed
            activeTags={activeTags}
            newsType={newsType}
            searchQuery={searchQuery}
          />
        </MainContent>

        <Sidebar>
          <StickyContainer>
            <NewsSearch onSearch={handleSearch} />
            <TagCloud
              activeTags={activeTags}
              onTagToggle={toggleTag}
              onClear={clearTags}
            />
          </StickyContainer>
        </Sidebar>
      </LayoutContainer>

      {/* 调试面板（默认隐藏） */}
      <DebugPanel visible={false} />
    </>
  );
}