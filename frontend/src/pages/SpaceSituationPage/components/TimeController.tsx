import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { CloseOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Modal } from 'antd';

interface TimeControllerProps {
  currentTime: Date;
  multiplier: number;
  isPlaying: boolean;
  isDisabled?: boolean;
  isManualTimeControl?: boolean;
  onMultiplierChange: (multiplier: number) => void;
  onPlayPauseToggle: () => void;
  onRealTimeClick?: () => void;
}

const Container = styled.div`
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 100;
  width: 80px;
  height: 36px;
  display: flex;
  align-items: flex-end;
`;

const SpeedButton = styled.button<{ $isPlaying: boolean; $isDisabled?: boolean }>`
  width: 100%;
  height: 32px;
  margin-bottom: 1px;
  transform: translateY(-2px);
  background: linear-gradient(
    180deg, 
    rgb(40, 40, 40) 0%,
    rgb(20, 20, 20) 40%,
    rgb(0, 0, 0) 100%
  );  // 更明显的渐变过渡
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-top-color: rgba(255, 255, 255, 0.4);  // 增强顶部高光
  border-bottom-color: rgba(0, 0, 0, 0.8);     // 加深底部边框
  border-radius: 3px 3px 0 0;  // 稍微增加圆角
  color: white;
  padding: 0 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: ${props => props.$isDisabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  opacity: ${props => props.$isDisabled ? 0.5 : 1};
  box-shadow: 
    0 -2px 4px rgba(0, 0, 0, 0.4),        // 外部阴影
    inset 0 1px 1px rgba(255, 255, 255, 0.2),  // 顶部内阴影
    inset 0 -2px 2px rgba(0, 0, 0, 0.4),       // 底部内阴影
    inset 1px 0 1px rgba(255, 255, 255, 0.05), // 左侧内阴影
    inset -1px 0 1px rgba(0, 0, 0, 0.3);       // 右侧内阴影

  &:hover {
    background: ${props => props.$isDisabled ? 'linear-gradient(180deg, rgb(40, 40, 40) 0%, rgb(20, 20, 20) 40%, rgb(0, 0, 0) 100%)' : 'linear-gradient(180deg, rgb(50, 50, 50) 0%, rgb(30, 30, 30) 40%, rgb(10, 10, 10) 100%)'};
    border-top-color: ${props => props.$isDisabled ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.5)'};
    border-bottom-color: ${props => props.$isDisabled ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.9)'};
    box-shadow: ${props => props.$isDisabled ? 
      '0 -2px 4px rgba(0, 0, 0, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.2), inset 0 -2px 2px rgba(0, 0, 0, 0.4), inset 1px 0 1px rgba(255, 255, 255, 0.05), inset -1px 0 1px rgba(0, 0, 0, 0.3)' : 
      '0 -2px 6px rgba(0, 0, 0, 0.5), inset 0 1px 2px rgba(255, 255, 255, 0.3), inset 0 -2px 3px rgba(0, 0, 0, 0.5), inset 1px 0 1px rgba(255, 255, 255, 0.1), inset -1px 0 1px rgba(0, 0, 0, 0.4)'};
  }

  &:active {
    background: ${props => props.$isDisabled ? 
      'linear-gradient(180deg, rgb(40, 40, 40) 0%, rgb(20, 20, 20) 40%, rgb(0, 0, 0) 100%)' : 
      'linear-gradient(180deg, rgb(15, 15, 15) 0%, rgb(25, 25, 25) 40%, rgb(0, 0, 0) 100%)'};
    border-top-color: ${props => props.$isDisabled ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.3)'};
    transform: ${props => props.$isDisabled ? 'translateY(-2px)' : 'translateY(-1px)'};
    box-shadow: ${props => props.$isDisabled ? 
      '0 -2px 4px rgba(0, 0, 0, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.2), inset 0 -2px 2px rgba(0, 0, 0, 0.4), inset 1px 0 1px rgba(255, 255, 255, 0.05), inset -1px 0 1px rgba(0, 0, 0, 0.3)' : 
      '0 -1px 3px rgba(0, 0, 0, 0.4), inset 0 1px 3px rgba(0, 0, 0, 0.3), inset 0 -1px 2px rgba(0, 0, 0, 0.4), inset 1px 0 1px rgba(0, 0, 0, 0.2), inset -1px 0 1px rgba(0, 0, 0, 0.2)'};
  }
`;

const SpeedPanel = styled.div`
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  position: absolute;
  bottom: calc(100% + 8px);
  right: 0;
  width: 280px;
  backdrop-filter: blur(16px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  background: transparent;
  border-radius: 4px 4px 0 0;
  
  span {
    font-size: 15px;
    font-weight: 500;
    color: #e6e6e6;
  }

  .close-button {
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    transition: all 0.2s;
    
    &:hover {
      color: white;
      transform: scale(1.1);
    }
  }
`;

const TabContainer = styled.div`
  display: flex;
  padding: 12px;
  gap: 8px;
`;

const Tab = styled.button<{ $active: boolean }>`
  flex: 1;
  padding: 8px 12px;
  background: ${props => props.$active ? 'rgba(73, 100, 255, 0.15)' : 'transparent'};
  border: 1px solid ${props => props.$active ? 'rgba(73, 100, 255, 0.4)' : 'rgba(255, 255, 255, 0.15)'};
  border-radius: 3px;
  color: ${props => props.$active ? '#7B9FFF' : '#e6e6e6'};
  cursor: pointer;
  transition: all 0.2s;
  font-weight: ${props => props.$active ? '500' : 'normal'};

  &:hover {
    border-color: ${props => props.$active ? 'rgba(73, 100, 255, 0.6)' : 'rgba(255, 255, 255, 0.3)'};
    background: ${props => props.$active ? 'rgba(73, 100, 255, 0.2)' : 'rgba(255, 255, 255, 0.05)'};
  }
`;

const CurrentTime = styled.div`
  padding: 16px;
  color: #e6e6e6;
  text-align: center;
  font-size: 16px;
  font-family: 'Roboto Mono', monospace;
  letter-spacing: 0.5px;
`;

const SpeedControl = styled.div`
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  
  .speed-value {
    flex: 1;
    text-align: center;
    font-size: 20px;
    color: #e6e6e6;
    font-weight: 500;
    font-family: 'Roboto Mono', monospace;
  }

  button {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 3px;
    color: #e6e6e6;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      border-color: rgba(255, 255, 255, 0.3);
      background: rgba(255, 255, 255, 0.05);
      transform: scale(1.05);
    }

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
`;

export function TimeController({
  currentTime,
  multiplier,
  isPlaying,
  isDisabled = false,
  isManualTimeControl = false,
  onMultiplierChange,
  onPlayPauseToggle,
  onRealTimeClick
}: TimeControllerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'realtime' | 'custom'>('realtime');
  const [customMultiplier, setCustomMultiplier] = useState(multiplier);
  const panelRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [displayTime, setDisplayTime] = useState(currentTime);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (activeTab === 'realtime' && isPlaying) {
      timer = setInterval(() => {
        setDisplayTime(new Date());
      }, 1000);
    }
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [activeTab, isPlaying]);

  useEffect(() => {
    setDisplayTime(currentTime);
  }, [currentTime]);

  useEffect(() => {
    if (activeTab === 'custom') {
      setCustomMultiplier(multiplier);
    }
  }, [multiplier, activeTab]);

  const handleMouseEnter = () => {
    if (isDisabled) {
      Modal.info({
        title: '功能暂时不可用',
        content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
        okText: '我知道了',
        maskClosable: true,
      });
      return;
    }
    
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    if (isDisabled) return;
    
    hoverTimeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  const handlePanelMouseEnter = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  };

  const handlePanelMouseLeave = () => {
    setIsOpen(false);
  };

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const handlePlayPauseClick = () => {
    onPlayPauseToggle();
  };

  const formatTime = (date: Date) => {
    const pad = (num: number) => String(num).padStart(2, '0');
    
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hour = pad(date.getHours());
    const minute = pad(date.getMinutes());
    const second = pad(date.getSeconds());
    
    return `${year}年${month}月${day}日 ${hour}:${minute}:${second}`;
  };

  const getDisplayText = () => {
    if (!isPlaying || multiplier === 0) {
      return '已暂停';
    }
    
    // 如果是手动控制时间，显示"模拟"
    if (isManualTimeControl) {
      return multiplier === 1 ? '模拟' : `${multiplier}x 模拟`;
    }
    
    return multiplier === 1 ? '实时' : `${multiplier}x 倍速`;
  };

  const handleSpeedChange = (delta: number) => {
    const speeds = [0, 0.1, 0.5, 1, 2, 5, 10, 20, 50, 100];
    const currentIndex = speeds.indexOf(customMultiplier);
    
    let newIndex = currentIndex;
    if (currentIndex === -1) {
      newIndex = speeds.findIndex(speed => speed >= customMultiplier);
      if (newIndex === -1) newIndex = speeds.length - 1;
    }

    newIndex = Math.max(0, Math.min(speeds.length - 1, newIndex + delta));
    const newSpeed = speeds[newIndex];
    
    if (customMultiplier === 0 && newSpeed !== 0 && !isPlaying) {
      onPlayPauseToggle();
    }
    else if (newSpeed === 0 && isPlaying) {
      onPlayPauseToggle();
    }
    
    setCustomMultiplier(newSpeed);
    onMultiplierChange(newSpeed);
  };

  const handleTabClick = (tab: 'realtime' | 'custom') => {
    setActiveTab(tab);
    
    if (tab === 'realtime') {
      onMultiplierChange(1);
      
      if (!isPlaying) {
        onPlayPauseToggle();
      }
      
      if (onRealTimeClick) {
        onRealTimeClick();
        console.log('触发实时功能，将卫星同步到当前系统时间');
      }
    } else if (tab === 'custom') {
      onMultiplierChange(customMultiplier);
    }
  };

  const renderCustomControls = () => (
    <SpeedControl>
      <button 
        onClick={() => handleSpeedChange(-1)}
        disabled={customMultiplier <= 0}
      >
        <MinusOutlined />
      </button>
      <div className="speed-value">
        {customMultiplier === 0 ? '0x' : `${customMultiplier}x`}
      </div>
      <button 
        onClick={() => handleSpeedChange(1)}
        disabled={customMultiplier >= 100}
      >
        <PlusOutlined />
      </button>
    </SpeedControl>
  );

  const renderRealtimeControls = () => (
    <CurrentTime>
      {formatTime(displayTime)}
    </CurrentTime>
  );

  return (
    <Container>
      <SpeedButton
        ref={buttonRef}
        $isPlaying={isPlaying}
        $isDisabled={isDisabled}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {getDisplayText()}
      </SpeedButton>

      {isOpen && !isDisabled && (
        <SpeedPanel 
          ref={panelRef}
          onMouseEnter={handlePanelMouseEnter}
          onMouseLeave={handlePanelMouseLeave}
        >
          <PanelHeader>
            <span>卫星速度</span>
            <CloseOutlined className="close-button" onClick={() => setIsOpen(false)} />
          </PanelHeader>

          <TabContainer>
            <Tab
              $active={activeTab === 'realtime'}
              onClick={() => handleTabClick('realtime')}
            >
              实时
            </Tab>
            <Tab
              $active={activeTab === 'custom'}
              onClick={() => handleTabClick('custom')}
            >
              自定义
            </Tab>
          </TabContainer>

          {activeTab === 'realtime' ? renderRealtimeControls() : renderCustomControls()}
        </SpeedPanel>
      )}
    </Container>
  );
} 