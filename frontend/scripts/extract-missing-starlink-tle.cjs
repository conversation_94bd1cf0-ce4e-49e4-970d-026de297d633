#!/usr/bin/env node

/**
 * 提取没有匹配到TLE数据的Starlink卫星NORAD ID
 * 
 * 功能：
 * 1. 获取Starlink星座的所有NORAD ID列表
 * 2. 获取所有TLE数据中的NORAD ID列表  
 * 3. 找出差集（在星座列表中但不在TLE列表中的NORAD ID）
 * 4. 将结果输出到txt文件
 * 
 * 使用方法：
 * cd frontend
 * node scripts/extract-missing-starlink-tle.js
 */

const fs = require('fs');
const path = require('path');

// 模拟数据管理器的功能，直接调用API
const { apiService } = require('../src/services/apiService');

/**
 * 获取星座的卫星NORAD ID列表
 */
async function getStarlinkNoradIds() {
  try {
    console.log('📡 获取Starlink星座数据...');
    
    // 1. 获取星座列表
    const constellationResponse = await apiService.get('/local/constellation/with-tle');
    
    if (!constellationResponse.constellations) {
      throw new Error('无法获取星座列表');
    }
    
    // 找到Starlink星座
    const starlinkConstellation = constellationResponse.constellations.find(
      c => c.name.toLowerCase() === 'starlink'
    );
    
    if (!starlinkConstellation) {
      throw new Error('未找到Starlink星座');
    }
    
    console.log(`📊 Starlink星座包含 ${starlinkConstellation.satelliteCount} 颗卫星`);
    
    // 2. 获取Starlink下的所有卫星
    let allSatellites = [];
    let currentPage = 1;
    let hasMorePages = true;
    
    while (hasMorePages) {
      console.log(`📡 获取第 ${currentPage} 页卫星数据...`);
      
      const satelliteResponse = await apiService.post('/api/v1/database/filter-satellites', {
        constellationName: 'Starlink',
        hasTleData: false, // 获取所有卫星，不只是有TLE的
        page: currentPage,
        limit: 100
      });
      
      if (satelliteResponse.success && satelliteResponse.results.length > 0) {
        allSatellites = [...allSatellites, ...satelliteResponse.results];
        
        // 检查是否还有更多页
        if (allSatellites.length >= satelliteResponse.total || satelliteResponse.results.length < 100) {
          hasMorePages = false;
        } else {
          currentPage++;
        }
      } else {
        hasMorePages = false;
      }
    }
    
    // 3. 提取NORAD ID
    const noradIds = [];
    for (const satellite of allSatellites) {
      if (satellite.norad_id && satellite.norad_id.length > 0) {
        noradIds.push(satellite.norad_id[0].value);
      }
    }
    
    console.log(`📊 总共获取到 ${noradIds.length} 个Starlink卫星的NORAD ID`);
    return noradIds;
    
  } catch (error) {
    console.error('❌ 获取Starlink NORAD ID失败:', error);
    throw error;
  }
}

/**
 * 获取所有TLE数据的NORAD ID
 */
async function getAllTleNoradIds() {
  try {
    console.log('🛰️ 获取所有TLE数据...');
    
    const response = await apiService.post('/orbit/bulk-tle/all?sampleMode=false', {});
    
    if (!response.success || !response.results) {
      throw new Error('无法获取TLE数据');
    }
    
    const tleNoradIds = response.results.map(item => item.norad_id);
    
    console.log(`📊 TLE数据库包含 ${tleNoradIds.length} 颗卫星的轨道数据`);
    return tleNoradIds;
    
  } catch (error) {
    console.error('❌ 获取TLE数据失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始提取没有匹配到TLE数据的Starlink卫星NORAD ID...');
  
  try {
    // 1. 获取Starlink的所有NORAD ID
    const starlinkNoradIds = await getStarlinkNoradIds();
    
    if (starlinkNoradIds.length === 0) {
      console.error('❌ 未找到Starlink星座数据');
      process.exit(1);
    }
    
    // 2. 获取所有TLE数据的NORAD ID
    const tleNoradIds = await getAllTleNoradIds();
    
    if (tleNoradIds.length === 0) {
      console.error('❌ 未找到TLE数据');
      process.exit(1);
    }
    
    // 3. 创建TLE NORAD ID的Set用于快速查找
    const tleNoradIdSet = new Set(tleNoradIds);
    
    // 4. 找出缺少TLE数据的NORAD ID
    const missingNoradIds = starlinkNoradIds.filter(id => !tleNoradIdSet.has(id));
    
    console.log(`📊 统计结果:`);
    console.log(`   - Starlink星座总卫星数: ${starlinkNoradIds.length}`);
    console.log(`   - 有TLE数据的卫星数: ${starlinkNoradIds.length - missingNoradIds.length}`);
    console.log(`   - 缺少TLE数据的卫星数: ${missingNoradIds.length}`);
    
    // 5. 生成输出文件
    const outputDir = path.join(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const outputFile = path.join(outputDir, `starlink-missing-tle-${timestamp}.txt`);
    
    // 6. 准备输出内容
    const outputLines = [
      `Starlink卫星缺少TLE数据的NORAD ID列表`,
      `生成时间: ${new Date().toLocaleString('zh-CN')}`,
      ``,
      `统计信息:`,
      `- Starlink星座总卫星数: ${starlinkNoradIds.length}`,
      `- 有TLE数据的卫星数: ${starlinkNoradIds.length - missingNoradIds.length}`,
      `- 缺少TLE数据的卫星数: ${missingNoradIds.length}`,
      `- 数据完整率: ${((starlinkNoradIds.length - missingNoradIds.length) / starlinkNoradIds.length * 100).toFixed(2)}%`,
      ``,
      `缺少TLE数据的NORAD ID列表:`,
      `========================================`,
      ...missingNoradIds.map(id => id.toString())
    ];
    
    // 7. 写入文件
    fs.writeFileSync(outputFile, outputLines.join('\n'), 'utf-8');
    
    console.log(`✅ 结果已保存到: ${outputFile}`);
    console.log(`📁 文件包含 ${missingNoradIds.length} 个缺少TLE数据的NORAD ID`);
    
    // 8. 显示一些示例
    if (missingNoradIds.length > 0) {
      console.log(`\n📋 前10个缺少TLE数据的NORAD ID示例:`);
      missingNoradIds.slice(0, 10).forEach((id, index) => {
        console.log(`   ${index + 1}. ${id}`);
      });
      
      if (missingNoradIds.length > 10) {
        console.log(`   ... 还有 ${missingNoradIds.length - 10} 个`);
      }
    }
    
    // 9. 额外分析：检查是否有重复的NORAD ID
    const starlinkIdSet = new Set(starlinkNoradIds);
    if (starlinkIdSet.size !== starlinkNoradIds.length) {
      console.log(`⚠️ 注意: Starlink星座数据中存在重复的NORAD ID`);
      console.log(`   - 原始数量: ${starlinkNoradIds.length}`);
      console.log(`   - 去重后数量: ${starlinkIdSet.size}`);
    }
    
    console.log(`\n🎉 提取完成！`);
    
  } catch (error) {
    console.error('❌ 提取过程中发生错误:', error);
    console.error('错误详情:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
} 