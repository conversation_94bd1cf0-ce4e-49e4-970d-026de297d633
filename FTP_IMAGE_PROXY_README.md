# FTP图片代理服务实现文档

## 概述

本文档描述了为太空大数据平台新闻系统实现的FTP图片代理服务，该服务允许前端通过HTTP请求访问存储在FTP服务器上的新闻图片。

## 架构设计

### 系统组件

1. **FTP图片代理服务器** (`image-proxy-server.js`)
   - 基于Express.js的HTTP服务器
   - 监听端口：3002
   - 提供FTP到HTTP的图片代理功能

2. **前端图片服务** (`frontend/src/services/imageService.ts`)
   - 图片URL生成和管理
   - 错误处理和回退机制
   - 图片预加载功能

3. **更新的新闻组件**
   - 支持`jpg_path`字段的新闻卡片
   - 自动回退到默认图片的错误处理

## FTP服务器配置

```javascript
const FTP_CONFIG = {
  host: '**************',
  user: 'yulingjing',
  password: 'readnewsjpg123',
  secure: false // 使用标准FTP
};
```

## API接口

### 图片代理接口

**GET** `/image?path={imagePath}`

获取FTP服务器上的图片文件。

**参数：**
- `path` (string, required): FTP服务器上的图片文件路径

**响应：**
- 成功：返回图片二进制数据，设置适当的Content-Type
- 失败：返回JSON错误信息

**示例：**
```
GET http://localhost:3002/image?path=/news/2024/03/image001.jpg
```

### 健康检查接口

**GET** `/health`

检查代理服务器状态。

**响应：**
```json
{
  "status": "ok",
  "service": "FTP Image Proxy",
  "cache": {
    "size": 5,
    "maxSize": 100
  },
  "timestamp": "2024-03-15T10:30:00.000Z"
}
```

### 缓存管理接口

**GET** `/cache/status` - 查看缓存状态
**DELETE** `/cache` - 清理所有缓存

## 缓存机制

### 内存缓存
- **缓存时长**：24小时
- **最大缓存数量**：100张图片
- **缓存策略**：LRU（最近最少使用）
- **自动清理**：每小时清理过期缓存

### 浏览器缓存
- **Cache-Control**：`public, max-age=86400` (24小时)
- **缓存头**：`X-Cache: HIT/MISS` 标识缓存状态

## 前端集成

### 图片服务使用

```typescript
import { getImageUrl, createImageLoader } from '../services/imageService';

// 基本用法
const imageUrl = getImageUrl(jpgPaths);

// 带错误处理的用法
const { imageUrl, onError } = createImageLoader(jpgPaths);

// 在组件中使用
<img src={imageUrl} onError={onError} alt="新闻图片" />
```

### 新闻数据结构

```typescript
interface ESNewsItem {
  _id: string;
  title: string;
  summary: string;
  thumbnail_url: string;  // 原有缩略图URL
  jpg_path: string[];     // 新增：FTP图片路径数组
  // ... 其他字段
}
```

## 部署和运行

### 启动代理服务器

```bash
# 开发环境
npm run dev:proxy

# 生产环境
npm run start:proxy

# 或直接运行
node image-proxy-server.js
```

### 测试FTP连接

```bash
# 运行测试脚本
node test-ftp-proxy.js
```

### 前端开发

```bash
# 启动前端开发服务器
cd frontend
npm run dev
```

## 错误处理

### FTP连接错误
- 自动重试机制
- 详细错误日志
- 优雅降级到默认图片

### 图片加载错误
- 前端自动回退到默认太空图片
- 错误事件处理
- 用户友好的错误提示

### 网络错误
- 超时处理（10秒）
- 连接失败重试
- 缓存机制减少网络请求

## 性能优化

### 服务器端
1. **内存缓存**：减少重复FTP请求
2. **连接复用**：优化FTP连接管理
3. **压缩传输**：支持gzip压缩
4. **并发控制**：限制同时FTP连接数

### 客户端
1. **图片预加载**：提前加载关键图片
2. **懒加载**：按需加载图片
3. **浏览器缓存**：利用HTTP缓存机制
4. **错误回退**：快速显示默认图片

## 监控和日志

### 服务器日志
- FTP连接状态
- 图片下载成功/失败
- 缓存命中率
- 错误详情

### 前端日志
- 图片加载状态
- 错误回退情况
- 性能指标

## 安全考虑

### FTP安全
- 使用专用FTP账户
- 限制访问权限
- 定期更换密码

### 代理服务安全
- CORS配置
- 请求频率限制
- 输入验证和清理

### 数据安全
- 不缓存敏感图片
- 定期清理缓存
- 访问日志记录

## 故障排除

### 常见问题

1. **FTP连接失败**
   - 检查网络连接
   - 验证FTP服务器配置
   - 查看防火墙设置

2. **图片加载缓慢**
   - 检查FTP服务器性能
   - 优化图片大小
   - 增加缓存时间

3. **代理服务器无响应**
   - 检查端口占用
   - 重启代理服务
   - 查看错误日志

### 调试工具

```bash
# 检查代理服务器状态
curl http://localhost:3002/health

# 测试图片下载
curl "http://localhost:3002/image?path=/path/to/image.jpg" -o test.jpg

# 查看缓存状态
curl http://localhost:3002/cache/status
```

## 未来改进

### 功能增强
1. **图片处理**：自动缩放、格式转换
2. **CDN集成**：提高全球访问速度
3. **批量下载**：支持多图片并行下载
4. **智能缓存**：基于访问频率的缓存策略

### 性能优化
1. **Redis缓存**：替换内存缓存
2. **负载均衡**：多实例部署
3. **图片压缩**：减少传输大小
4. **预热机制**：预加载热门图片

## 版本历史

- **v1.0.0** (2024-03-15): 初始版本，基本FTP代理功能
- 支持jpg_path字段
- 内存缓存机制
- 错误处理和回退
- 健康检查接口
