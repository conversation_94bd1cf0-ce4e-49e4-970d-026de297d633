#!/bin/bash

# 启动脚本：同时运行FTP图片代理服务器和前端开发服务器
# Usage: ./start-with-proxy.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js是否安装
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_info "Node.js 版本: $node_version"
}

# 检查npm是否安装
check_npm() {
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    local npm_version=$(npm --version)
    log_info "npm 版本: $npm_version"
}

# 检查依赖是否安装
check_dependencies() {
    log_info "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_warning "根目录依赖未安装，正在安装..."
        npm install
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        log_warning "前端依赖未安装，正在安装..."
        cd frontend && npm install && cd ..
    fi
    
    log_success "依赖检查完成"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用 ($service_name)"
        log_info "正在尝试终止占用端口的进程..."
        
        # 尝试优雅地终止进程
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        if [ ! -z "$pid" ]; then
            kill -TERM $pid 2>/dev/null || true
            sleep 2
            
            # 如果进程仍在运行，强制终止
            if kill -0 $pid 2>/dev/null; then
                kill -KILL $pid 2>/dev/null || true
                log_warning "强制终止了占用端口 $port 的进程"
            else
                log_success "成功释放端口 $port"
            fi
        fi
    fi
}

# 启动FTP图片代理服务器
start_proxy_server() {
    log_info "启动FTP图片代理服务器..."
    
    # 检查端口3002
    check_port 3002 "FTP图片代理服务器"
    
    # 启动代理服务器（后台运行）
    nohup node image-proxy-server.js > proxy-server.log 2>&1 &
    local proxy_pid=$!
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否成功启动
    if kill -0 $proxy_pid 2>/dev/null; then
        log_success "FTP图片代理服务器启动成功 (PID: $proxy_pid, 端口: 3002)"
        echo $proxy_pid > .proxy-server.pid
        
        # 测试健康检查
        if curl -s http://localhost:3002/health > /dev/null 2>&1; then
            log_success "代理服务器健康检查通过"
        else
            log_warning "代理服务器健康检查失败，但服务器正在运行"
        fi
    else
        log_error "FTP图片代理服务器启动失败"
        return 1
    fi
}

# 启动前端开发服务器
start_frontend() {
    log_info "启动前端开发服务器..."
    
    # 检查端口5175
    check_port 5175 "前端开发服务器"
    
    # 进入前端目录并启动开发服务器
    cd frontend
    
    log_info "正在启动前端开发服务器，请稍候..."
    npm run dev
}

# 清理函数
cleanup() {
    log_info "正在清理进程..."
    
    # 终止代理服务器
    if [ -f ".proxy-server.pid" ]; then
        local proxy_pid=$(cat .proxy-server.pid)
        if kill -0 $proxy_pid 2>/dev/null; then
            log_info "终止FTP图片代理服务器 (PID: $proxy_pid)"
            kill -TERM $proxy_pid 2>/dev/null || true
            sleep 2
            
            # 如果进程仍在运行，强制终止
            if kill -0 $proxy_pid 2>/dev/null; then
                kill -KILL $proxy_pid 2>/dev/null || true
            fi
        fi
        rm -f .proxy-server.pid
    fi
    
    log_success "清理完成"
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "🚀 太空大数据平台 - FTP图片代理服务启动脚本"
    echo "================================================"
    
    # 环境检查
    check_node
    check_npm
    check_dependencies
    
    echo ""
    log_info "开始启动服务..."
    
    # 启动代理服务器
    start_proxy_server
    
    echo ""
    log_info "代理服务器已启动，现在启动前端开发服务器..."
    log_info "前端服务器启动后，您可以访问："
    log_info "  - 前端应用: http://localhost:5175"
    log_info "  - 代理服务器健康检查: http://localhost:3002/health"
    log_info "  - 代理服务器缓存状态: http://localhost:3002/cache/status"
    echo ""
    log_warning "按 Ctrl+C 可以同时停止两个服务器"
    echo ""
    
    # 启动前端（这会阻塞直到用户按Ctrl+C）
    start_frontend
}

# 检查是否有帮助参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "太空大数据平台 - FTP图片代理服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -t, --test     仅测试FTP连接，不启动服务器"
    echo ""
    echo "功能:"
    echo "  - 自动检查和安装依赖"
    echo "  - 启动FTP图片代理服务器 (端口3002)"
    echo "  - 启动前端开发服务器 (端口5175)"
    echo "  - 优雅地处理进程清理"
    echo ""
    echo "服务地址:"
    echo "  - 前端应用: http://localhost:5175"
    echo "  - 代理服务器: http://localhost:3002"
    echo ""
    exit 0
fi

# 检查是否有测试参数
if [[ "$1" == "-t" || "$1" == "--test" ]]; then
    log_info "运行FTP连接测试..."
    node test-ftp-proxy.js
    exit $?
fi

# 运行主函数
main
