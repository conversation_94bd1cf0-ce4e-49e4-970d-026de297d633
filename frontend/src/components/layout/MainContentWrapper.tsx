import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

const Wrapper = styled.main`
  position: fixed;
  top: 64px; // 导航栏高度
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; // 确保在导航栏下方
  overflow-y: auto; // 在这里处理滚动

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
`;

const ContentContainer = styled.div`
  max-width: 1280px;
  margin: 0 auto;
  padding: 16px;
  min-height: 100%;
  
  /* 响应式padding */
  @media (min-width: 640px) {
    padding: 20px;
  }
  
  @media (min-width: 768px) {
    padding: 24px;
  }
  
  @media (min-width: 1024px) {
    padding: 32px;
  }
`;

interface MainContentWrapperProps {
  children: React.ReactNode;
}

export function MainContentWrapper({ children }: MainContentWrapperProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);

  // 添加滚动监听以帮助调试
  useEffect(() => {
    const wrapper = wrapperRef.current;
    if (!wrapper) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = wrapper;
      console.log('Scroll Debug Info:', {
        scrollTop,
        scrollHeight,
        clientHeight,
        wrapperTop: wrapper.getBoundingClientRect().top,
        wrapperHeight: wrapper.offsetHeight,
        viewportHeight: window.innerHeight,
        navbarHeight: 64
      });
    };

    wrapper.addEventListener('scroll', handleScroll);
    return () => wrapper.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <Wrapper ref={wrapperRef} data-scroll-container="main">
      <ContentContainer>
        {children}
      </ContentContainer>
    </Wrapper>
  );
} 