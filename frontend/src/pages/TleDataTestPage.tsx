/**
 * TLE数据管理测试页面
 * 用于测试和验证TLE数据的自动获取和存储功能
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Descriptions, Badge, message, Progress, Typography, Space, Divider } from 'antd';
import { ReloadOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { tleDataManager, TleDataStatus } from '../services/tleDataManager';
import { indexedDBHelper } from '../utils/indexedDBHelper';

const { Title, Text } = Typography;

export function TleDataTestPage() {
  const [status, setStatus] = useState<TleDataStatus>({
    hasData: false,
    isExpired: true,
    lastUpdated: null,
    nextUpdate: null,
    satelliteCount: 0,
    dataAge: '无数据',
    isUpdating: false,
    lastError: null
  });
  const [updateProgress, setUpdateProgress] = useState(0);
  const [updateMessage, setUpdateMessage] = useState('');

  useEffect(() => {
    // 设置事件监听器
    tleDataManager.setListeners({
      onUpdateStart: () => {
        console.log('TLE数据开始更新');
        setUpdateProgress(0);
        setUpdateMessage('开始更新...');
      },
      onUpdateProgress: (progress, message) => {
        console.log(`TLE数据更新进度: ${progress}% - ${message}`);
        setUpdateProgress(progress);
        setUpdateMessage(message);
      },
      onUpdateComplete: (data) => {
        console.log('TLE数据更新完成:', data.total);
        setUpdateProgress(100);
        setUpdateMessage('更新完成');
        message.success(`TLE数据更新完成，共 ${data.total} 颗卫星`);
      },
      onUpdateError: (error) => {
        console.error('TLE数据更新失败:', error);
        setUpdateMessage(`更新失败: ${error}`);
        message.error(`TLE数据更新失败: ${error}`);
      },
      onStatusChange: (newStatus) => {
        setStatus(newStatus);
      }
    });

    // 获取初始状态
    const updateStatus = async () => {
      try {
        const cachedData = await indexedDBHelper.getTleData();
        const currentStatus = tleDataManager.getDataStatus(cachedData);
        setStatus(currentStatus);
      } catch (error) {
        console.error('获取状态失败:', error);
      }
    };

    updateStatus();
  }, []);

  const handleRefresh = async () => {
    try {
      await tleDataManager.refreshData();
    } catch (error) {
      console.error('刷新失败:', error);
    }
  };

  const handleClearCache = async () => {
    try {
      await tleDataManager.clearCache();
      message.success('缓存已清除');
    } catch (error) {
      console.error('清除缓存失败:', error);
      message.error('清除缓存失败');
    }
  };

  const handleTestGetData = async () => {
    try {
      const data = await tleDataManager.getTleData();
      message.success(`获取到 ${data.length} 颗卫星的TLE数据`);
      console.log('TLE数据样本:', data.slice(0, 3));
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    }
  };

  const getStatusColor = () => {
    if (status.lastError) return 'red';
    if (!status.hasData) return 'orange';
    if (status.isExpired) return 'orange';
    return 'green';
  };

  const getStatusText = () => {
    if (status.lastError) return '错误';
    if (!status.hasData) return '无数据';
    if (status.isExpired) return '已过期';
    return '正常';
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>TLE数据管理测试</Title>
      
      <Card title="数据状态" style={{ marginBottom: '16px' }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="状态">
            <Badge status={getStatusColor() as any} text={getStatusText()} />
          </Descriptions.Item>
          <Descriptions.Item label="卫星数量">
            {status.satelliteCount.toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="数据年龄">
            {status.dataAge}
          </Descriptions.Item>
          <Descriptions.Item label="最后更新">
            {status.lastUpdated ? new Date(status.lastUpdated).toLocaleString() : '无'}
          </Descriptions.Item>
          <Descriptions.Item label="下次更新">
            {status.nextUpdate ? new Date(status.nextUpdate).toLocaleString() : '无'}
          </Descriptions.Item>
          <Descriptions.Item label="更新状态">
            {status.isUpdating ? <Badge status="processing" text="更新中" /> : <Badge status="default" text="空闲" />}
          </Descriptions.Item>
        </Descriptions>
        
        {status.lastError && (
          <div style={{ marginTop: '16px' }}>
            <Text type="danger">
              <InfoCircleOutlined /> 错误信息: {status.lastError}
            </Text>
          </div>
        )}
      </Card>

      {status.isUpdating && (
        <Card title="更新进度" style={{ marginBottom: '16px' }}>
          <Progress percent={updateProgress} status="active" />
          <Text style={{ marginTop: '8px', display: 'block' }}>{updateMessage}</Text>
        </Card>
      )}

      <Card title="操作" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            loading={status.isUpdating}
            onClick={handleRefresh}
          >
            手动刷新
          </Button>
          
          <Button 
            icon={<InfoCircleOutlined />}
            onClick={handleTestGetData}
          >
            测试获取数据
          </Button>
          
          <Button 
            danger
            icon={<DeleteOutlined />}
            onClick={handleClearCache}
            disabled={status.isUpdating}
          >
            清除缓存
          </Button>
        </Space>
      </Card>

      <Card title="说明">
        <div>
          <p><strong>功能说明：</strong></p>
          <ul>
            <li>TLE数据管理器会每4小时自动从后端API获取最新的卫星TLE数据</li>
            <li>数据存储在浏览器的IndexedDB中，无需每次都请求API</li>
            <li>当用户选择"全部卫星"图层时，会优先使用本地缓存的数据</li>
            <li>如果本地数据不存在或已过期，会自动请求API获取最新数据</li>
          </ul>
          
          <Divider />
          
          <p><strong>API端点：</strong></p>
          <Text code>POST /orbit/bulk-tle/all?sampleMode=false</Text>
          
          <Divider />
          
          <p><strong>数据格式：</strong></p>
          <Text code>
            {JSON.stringify({
              cospar_id: "1969-036A",
              norad_id: 3889,
              epoch: "2025-06-06T21:09:36Z",
              time: "2025-06-05T22:52:03.498455+00:00",
              tle_raw: "Canyon 2\\n1 03889U 69036A   25157.88167092...",
              satellite_name: "Canyon 2",
              constellation_name: "GPS",
              orbital_elements: {
                inc_deg: 55.0,
                raan_deg: 123.4,
                ecc: 0.001,
                arg_peri_deg: 45.6,
                mean_anom_deg: 234.5,
                day_laps: 2.0,
                orbit_num: 12345,
                orbital_period_min: 717.97,
                sema_km: 26560.0,
                arg_alt_km: 20200.0,
                arg_apo_deg: 225.6,
                launch_time: "2023-01-15T10:30:00Z"
              }
            }, null, 2)}
          </Text>
        </div>
      </Card>
    </div>
  );
} 