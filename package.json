{"name": "spacedata-platform", "version": "1.0.0", "description": "太空大数据平台前端", "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install", "start:proxy": "node image-proxy-server.js", "dev:proxy": "node image-proxy-server.js"}, "private": true, "workspaces": ["frontend"], "dependencies": {"basic-ftp": "^5.0.5", "cors": "^2.8.5", "express": "^5.1.0", "rxjs": "^7.8.2"}}