import { useState, useEffect } from 'react';
import { NewsItem } from '../types';
import { mockNews } from '../data/mockNews';
import { useNewsReadStatus } from './useNewsReadStatus';
import { newsService, ESNewsItem } from '../../../services/newsService';
import { addDebugLog } from '../../../components/DebugPanel';
import { formatNewsDateTime } from '../../../utils/dateUtils';

// 已删除关注新闻功能，不再需要用户关键字
// const userKeywords = ['太空碎片', '空间站', '火箭发射'];

export function useNewsFeed(
  activeTags: string[] = [],
  newsType: 'latest' | 'military' = 'latest',
  searchQuery?: string
) {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const { isRead, readNewsIds } = useNewsReadStatus();

  // 监控 readNewsIds 变化
  useEffect(() => {
    console.log('[useNewsFeed] readNewsIds updated:', readNewsIds);
  }, [readNewsIds]);

  // 当 activeTags 变化时清空新闻列表并设置加载状态
  useEffect(() => {
    addDebugLog(`[useNewsFeed] activeTags changed, clearing news list: ${JSON.stringify(activeTags)}`);
    setNews([]);
    setIsLoading(true);
  }, [activeTags]);

  // 解析搜索查询为关键词数组
  const parseSearchQuery = (query: string): string[] => {
    if (!query) return [];
    // 使用空格或标点符号作为分隔符
    return query.split(/[\s,.;:!?，。；：！？、]+/).filter(keyword => keyword.trim() !== '');
  };

  useEffect(() => {
    const fetchNews = async () => {
      addDebugLog(`[useNewsFeed] Fetching news with params: ${JSON.stringify({ activeTags, newsType, searchQuery })}`);
      setIsLoading(true);

      // 解析搜索查询为关键词数组
      const keywords = searchQuery ? parseSearchQuery(searchQuery) : [];
      addDebugLog(`[useNewsFeed] Parsed search query "${searchQuery}" into keywords: ${JSON.stringify(keywords)}`);

      try {
        // 根据新闻类型选择不同的API调用
        let response;

        // 构建基本请求参数
        const requestParams: any = {
          page: 1,
          limit: 10
        };

        // 如果是军事新闻，添加 indexPatterns 参数
        if (newsType === 'military') {
          addDebugLog('[useNewsFeed] Fetching military news');
          requestParams.indexPatterns = [
            "*defence*",
            "*defense*"
          ];
        }

        // 设置主题筛选参数
        if (newsType === 'military') {
          // 军事新闻：添加"军事"主题词筛选，确保获取主题词中包含"军事"的新闻
          if (activeTags.length === 0) {
            // 没有其他筛选标签时，只筛选"军事"主题
            requestParams.themes = ['军事'];
            addDebugLog('[useNewsFeed] Adding default military theme filter: ["军事"]');
          } else {
            // 有其他筛选标签时，确保"军事"在标签列表中，同时包含用户选择的标签
            const militaryThemes = ['军事', ...activeTags.filter(tag => tag !== '军事')];
            requestParams.themes = militaryThemes;
            addDebugLog(`[useNewsFeed] Adding military themes with user filters: ${JSON.stringify(militaryThemes)}`);
          }
        } else if (activeTags.length > 0) {
          // 非军事新闻：如果有标签筛选，添加 themes 参数
          addDebugLog(`[useNewsFeed] Adding themes to request: ${JSON.stringify(activeTags)}`);
          requestParams.themes = activeTags;
        }

        // 如果有搜索关键词，添加 keywords 参数
        if (keywords.length > 0) {
          addDebugLog(`[useNewsFeed] Adding keywords to request: ${JSON.stringify(keywords)}`);
          requestParams.keywords = keywords;
        }

        // 根据新闻类型选择不同的API调用方法
        if (newsType === 'military') {
          addDebugLog(`[useNewsFeed] Final military news request params: ${JSON.stringify(requestParams)}`);
          response = await newsService.getMilitaryNewsList(requestParams);
          addDebugLog('[useNewsFeed] Military news API response received');
        } else {
          addDebugLog(`[useNewsFeed] Final news request params: ${JSON.stringify(requestParams)}`);
          response = await newsService.getNewsList(requestParams);
          addDebugLog('[useNewsFeed] News API response received');
        }

        if (response.success && response.data) {
          // 调试信息：查看原始数据
          console.log('[useNewsFeed] API response data:', response.data);
          console.log('[useNewsFeed] API response hits:', response.data.hits);
          console.log('[useNewsFeed] API response data array:', response.data.data);

          // 检查data数组是否存在（实际API结构使用data而不是hits）
          const newsData = response.data.data || response.data.hits || [];
          
          // 将API返回的数据转换为应用所需的格式
          const apiNews = newsData.map((item: ESNewsItem) => {
            // 格式化发布日期，将UTC时间转换为北京时间（UTC+8）
            const utcDate = new Date(item.publish_date.year, item.publish_date.month - 1, item.publish_date.day, item.publish_date.hour, item.publish_date.minute, item.publish_date.second);
            const publishDate = formatNewsDateTime(utcDate.toISOString());

            // 生成唯一ID
            const id = item._id;

            // 使用中文标题和摘要（如果有），否则使用英文
            const title = item.title_cn || item.title;
            const summary = item.summary_cn || item.summary;

            // 处理themes_cn字段，确保它是一个有效的数组
            let themesData = [];
            if (item.themes_cn && Array.isArray(item.themes_cn) && item.themes_cn.length > 0) {
              themesData = item.themes_cn;
            } else if (typeof item.themes_cn === 'string') {
              // 如果是字符串，尝试解析为JSON
              try {
                const parsed = JSON.parse(item.themes_cn);
                if (Array.isArray(parsed)) {
                  themesData = parsed;
                }
              } catch (e) {
                              // 如果不是JSON，将它作为单个元素的数组
              if (item.themes_cn) {
                themesData = [item.themes_cn];
              }
              }
            }

            // 调试信息
            console.log(`[useNewsFeed] Processed themes for ${id}:`, {
              original: item.themes_cn,
              processed: themesData
            });

            // 创建新闻项
            return {
              id,
              title,
              summary,
              content: item.content || '',
              content_cn: item.content_cn,
              title_cn: item.title_cn,
              title_en: item.title,
              summary_cn: item.summary_cn,
              summary_en: item.summary,
              date: publishDate,
              imageUrl: item.thumbnail_url || '',
              tags: themesData.length > 0 ? themesData : [],
              themes_cn: themesData, // 使用处理后的themes_cn字段
              isRead: isRead(id),
              matchedKeywords: [] as string[],
              author: {
                name: item.author || '未知作者',
                avatar: undefined
              },
              source: item.source,
              originalData: item // 保存原始数据以便详情页使用
            };
          });

          // 根据新闻类型过滤
          let filteredNews = apiNews;

          console.log(`[useNewsFeed] Before filtering: apiNews.length = ${apiNews.length}, newsType = ${newsType}`);

          if (newsType === 'military') {
            // 军事新闻：直接使用API返回的结果，不进行额外过滤
            // API已经通过indexPatterns参数过滤了军事相关新闻
            console.log('[useNewsFeed] Using military news directly from API without additional filtering');
            filteredNews = apiNews;
          } else {
            // latest 新闻：直接使用API返回的结果
            console.log('[useNewsFeed] Using latest news directly from API without filtering');
            filteredNews = apiNews;
          }

          console.log(`[useNewsFeed] After filtering: filteredNews.length = ${filteredNews.length}`);
          console.log('[useNewsFeed] Setting filtered news:', filteredNews);
          setNews(filteredNews);
          setHasMore(response.data.total > filteredNews.length);
        } else {
          // 如果API返回失败，使用模拟数据
          console.warn('API返回失败，使用模拟数据');

          // 使用模拟数据的逻辑
          let newsWithReadStatus = [...mockNews].map(news => {
            const readStatus = isRead(news.id);
            return {
              ...news,
              content: news.content || '', // 确保content不为undefined
              isRead: readStatus,
              matchedKeywords: [] as string[]
            };
          });

          // 模拟数据始终需要在前端进行标签过滤
          if (activeTags.length > 0) {
            console.log(`[useNewsFeed] Filtering mock data by tags:`, activeTags);
            newsWithReadStatus = newsWithReadStatus.filter(item => {
              // 检查 tags 字段
              const matchesTags = item.tags && activeTags.every(tag => item.tags.includes(tag));
              // 检查 themes_cn 字段
              const matchesThemes = item.themes_cn && activeTags.every(tag => item.themes_cn!.includes(tag));
              // 如果任一字段匹配，则保留该新闻
              return matchesTags || matchesThemes;
            });
          }

          // 按类型过滤并添加匹配关键字
          let filteredNews = newsWithReadStatus;

          // 模拟数据情况下，仍需要在前端进行过滤
          if (searchQuery) {
            const keywords = parseSearchQuery(searchQuery);
            console.log(`[useNewsFeed] Filtering mock data by keywords:`, keywords);

            filteredNews = filteredNews.filter(item => {
              // 检查标题、摘要和标签是否包含任何关键词
              return keywords.some(keyword => {
                const keywordLower = keyword.toLowerCase();
                return item.title.toLowerCase().includes(keywordLower) ||
                  item.summary.toLowerCase().includes(keywordLower) ||
                  item.tags.some(tag => tag.toLowerCase().includes(keywordLower));
              });
            });
          }

          setNews(filteredNews);
          setHasMore(filteredNews.length >= 10);
        }
      } catch (error) {
        console.error('[useNewsFeed] Failed to fetch news:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNews();
  }, [activeTags, newsType, searchQuery, isRead, readNewsIds]);

  // 强制更新新闻列表的已读状态
  useEffect(() => {
    console.log('[useNewsFeed] Read status update triggered with readNewsIds:', readNewsIds);

    setNews(prevNews => {
      // 如果没有新闻，不需要更新
      if (prevNews.length === 0) {
        console.log('[useNewsFeed] No news to update');
        return prevNews;
      }

      const updatedNews = prevNews.map(news => {
        const wasRead = news.isRead;
        const isNewsRead = readNewsIds.includes(news.id);

        // 只有当状态发生变化时才记录日志
        if (wasRead !== isNewsRead) {
          console.log(`[useNewsFeed] News ${news.id} read status changed:`, {
            from: wasRead,
            to: isNewsRead
          });
        }

        return {
          ...news,
          isRead: isNewsRead
        };
      });

      // 检查是否有任何新闻的状态发生了变化
      const hasChanges = updatedNews.some((news, index) =>
        news.isRead !== prevNews[index].isRead
      );

      if (hasChanges) {
        console.log('[useNewsFeed] News list updated with new read status');
        return updatedNews;
      } else {
        console.log('[useNewsFeed] No read status changes detected');
        return prevNews;
      }
    });
  }, [readNewsIds]);

  const loadMore = async () => {
    setIsLoading(true);
    try {
      // 加载更多新闻
      const currentPage = Math.ceil(news.length / 10) + 1;

      // 解析搜索查询为关键词数组
      const keywords = searchQuery ? parseSearchQuery(searchQuery) : [];
      console.log(`[useNewsFeed] loadMore: Parsed search query "${searchQuery}" into keywords:`, keywords);

      // 构建基本请求参数
      const requestParams: any = {
        page: currentPage,
        limit: 10
      };

      // 如果是军事新闻，添加 indexPatterns 参数
      if (newsType === 'military') {
        console.log('[useNewsFeed] loadMore: Fetching military news');
        requestParams.indexPatterns = [
          "*defence*",
          "*defense*"
        ];
      }

      // 设置主题筛选参数
      if (newsType === 'military') {
        // 军事新闻：添加"军事"主题词筛选，确保获取主题词中包含"军事"的新闻
        if (activeTags.length === 0) {
          // 没有其他筛选标签时，只筛选"军事"主题
          requestParams.themes = ['军事'];
          console.log('[useNewsFeed] loadMore: Adding default military theme filter: ["军事"]');
        } else {
          // 有其他筛选标签时，确保"军事"在标签列表中，同时包含用户选择的标签
          const militaryThemes = ['军事', ...activeTags.filter(tag => tag !== '军事')];
          requestParams.themes = militaryThemes;
          console.log(`[useNewsFeed] loadMore: Adding military themes with user filters: ${JSON.stringify(militaryThemes)}`);
        }
      } else if (activeTags.length > 0) {
        // 非军事新闻：如果有标签筛选，添加 themes 参数
        console.log(`[useNewsFeed] loadMore: Adding themes to request:`, activeTags);
        requestParams.themes = activeTags;
      }

      // 如果有搜索关键词，添加 keywords 参数
      if (keywords.length > 0) {
        console.log(`[useNewsFeed] loadMore: Adding keywords to request:`, keywords);
        requestParams.keywords = keywords;
      }

      // 根据新闻类型选择不同的API调用方法
      let response;
      if (newsType === 'military') {
        console.log(`[useNewsFeed] loadMore: Final military news request params:`, requestParams);
        response = await newsService.getMilitaryNewsList(requestParams);
        console.log('[useNewsFeed] Military news loadMore API response:', response);
      } else {
        console.log(`[useNewsFeed] loadMore: Final news request params:`, requestParams);
        response = await newsService.getNewsList(requestParams);
        console.log('[useNewsFeed] News loadMore API response:', response);
      }

      if (response.success && response.data) {
        // 检查data数组是否存在（实际API结构使用data而不是hits）
        const newsData = response.data.data || response.data.hits || [];
        
        // 将API返回的数据转换为应用所需的格式
        const apiNews = newsData.map((item: ESNewsItem) => {
          // 格式化发布日期，将UTC时间转换为北京时间（UTC+8）
          const utcDate = new Date(item.publish_date.year, item.publish_date.month - 1, item.publish_date.day, item.publish_date.hour, item.publish_date.minute, item.publish_date.second);
          const publishDate = formatNewsDateTime(utcDate.toISOString());

          // 生成唯一ID
          const id = item._id;

          // 使用中文标题和摘要（如果有），否则使用英文
          const title = item.title_cn || item.title;
          const summary = item.summary_cn || item.summary;

          // 处理themes_cn字段，确保它是一个有效的数组
          let themesData = [];
          if (item.themes_cn && Array.isArray(item.themes_cn) && item.themes_cn.length > 0) {
            themesData = item.themes_cn;
          } else if (typeof item.themes_cn === 'string') {
            // 如果是字符串，尝试解析为JSON
            try {
              const parsed = JSON.parse(item.themes_cn);
              if (Array.isArray(parsed)) {
                themesData = parsed;
              }
            } catch (e) {
              // 如果不是JSON，将它作为单个元素的数组
              themesData = [item.themes_cn];
            }
          }

          // 调试信息
          console.log(`[useNewsFeed] Processed themes for ${id} (loadMore):`, {
            original: item.themes_cn,
            processed: themesData
          });

          // 创建新闻项
          return {
            id,
            title,
            summary,
            content: item.content,
            content_cn: item.content_cn,
            title_cn: item.title_cn,
            title_en: item.title,
            summary_cn: item.summary_cn,
            summary_en: item.summary,
            date: publishDate,
            imageUrl: item.thumbnail_url || '',
            tags: themesData.length > 0 ? themesData : [],
            themes_cn: themesData, // 使用处理后的themes_cn字段
            isRead: isRead(id),
            matchedKeywords: [] as string[],
            author: {
              name: item.author || '未知作者',
              avatar: undefined
            },
            source: item.source,
            originalData: item // 保存原始数据以便详情页使用
          };
        });

        // 根据新闻类型过滤
        let filteredNews = apiNews;

        if (newsType === 'military') {
          // 军事新闻：直接使用API返回的结果，不进行额外过滤
          // API已经通过indexPatterns参数过滤了军事相关新闻
          console.log('[useNewsFeed] loadMore: Using military news directly from API without additional filtering');
          filteredNews = apiNews;

        }

        // 如果在 API 请求中已经包含了 themes 参数，则不需要再次过滤
        // 只有在使用模拟数据时才需要在前端进行过滤
        if (activeTags.length > 0 && newsType === 'military' && !response.success) {
          // 只有在 API 请求失败时才在前端进行标签过滤
          // 同时检查 tags 和 themes_cn 字段
          filteredNews = filteredNews.filter(item => {
            // 检查 tags 字段
            const matchesTags = item.tags && activeTags.every(tag => item.tags.includes(tag));
            // 检查 themes_cn 字段
            const matchesThemes = item.themes_cn && activeTags.every(tag => item.themes_cn!.includes(tag));
            // 如果任一字段匹配，则保留该新闻
            return matchesTags || matchesThemes;
          });
          console.log(`[useNewsFeed] loadMore: Filtered military news by tags (fallback): ${JSON.stringify(activeTags)}`, filteredNews);
        }

        // 不再需要前端过滤，因为API已经根据keywords参数过滤了结果
        // 保留这个注释作为提醒

        // 合并新闻列表
        setNews(prevNews => [...prevNews, ...filteredNews]);
        setHasMore(response.data.total > (news.length + filteredNews.length));
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('[useNewsFeed] Failed to load more news:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    news,
    isLoading,
    hasMore,
    loadMore
  };
}