import React, { useState, useEffect } from 'react';
import { parseContent, parseBilingualContent, isEntityDataAvailable } from '../../../../utils/contentParser';

interface NewsContentProps {
  content?: string;
  showOriginal?: boolean;
  content_cn?: string;
}

export function NewsContent({ content = '', showOriginal = false, content_cn }: NewsContentProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [entityDataReady, setEntityDataReady] = useState(false);

  useEffect(() => {
    // 异步检查实体数据状态，避免阻塞渲染
    const checkEntityData = async () => {
      try {
        const dataAvailable = isEntityDataAvailable();
        setEntityDataReady(dataAvailable);
      } catch (error) {
        console.warn('检查实体数据状态失败:', error);
        setEntityDataReady(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 延迟执行，避免阻塞初始渲染
    const timer = setTimeout(checkEntityData, 50);
    return () => clearTimeout(timer);
  }, [content, content_cn]);

  // 处理换行符并添加缩进，同时保持实体解析的兼容性
  const processContentWithFormatting = (rawContent: string): string => {
    if (!rawContent) return '<p class="indent-8">暂无内容</p>';
    
    try {
      // 如果实体数据可用，先进行实体解析
      let processedContent = rawContent;
      if (entityDataReady) {
        processedContent = parseContent(rawContent);
      }
      
      // 统一换行符格式，处理\r\n、\r、\n
      processedContent = processedContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // 按所有换行符分割成段落（包括单换行符和双换行符）
      const paragraphs = processedContent.split(/\n+/);
      
      const formattedParagraphs = paragraphs
        .filter(p => p.trim()) // 过滤空段落
        .map(paragraph => {
          const trimmedParagraph = paragraph.trim();
          return `<p class="indent-8 mb-3 sm:mb-4">${trimmedParagraph}</p>`;
        });
      
      return formattedParagraphs.join('') || '<p class="indent-8">暂无内容</p>';
      
    } catch (error) {
      console.warn('内容处理失败:', error);
      // 降级处理：只处理换行符，不进行实体解析
      const formattedContent = rawContent
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .split(/\n+/)
        .filter(p => p.trim())
        .map(paragraph => {
          const trimmedParagraph = paragraph.trim();
          return `<p class="indent-8 mb-3 sm:mb-4">${trimmedParagraph}</p>`;
        })
        .join('');
      
      return formattedContent || '<p class="indent-8">暂无内容</p>';
    }
  };

  const processedContent = React.useMemo(() => {
    // 如果没有内容，返回默认文本
    if (!content && !content_cn) {
      return '<p class="indent-8">暂无内容</p>';
    }

    // 选择要显示的内容
    const displayContent = showOriginal ? (content || '') : (content_cn || content || '');
    
    return processContentWithFormatting(displayContent);

  }, [content, content_cn, showOriginal, entityDataReady]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <span className="text-white/70 text-sm">加载内容中...</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className="prose prose-invert prose-sm sm:prose-base lg:prose-lg xl:prose-xl leading-relaxed max-w-none text-gray-200 [&>p]:leading-relaxed"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}