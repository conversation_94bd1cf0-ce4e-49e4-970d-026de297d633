#!/usr/bin/env node

/**
 * 简单的FTP连接测试
 */

const { Client } = require('basic-ftp');

async function simpleFTPTest() {
  const client = new Client();
  
  // 启用详细日志
  client.ftp.verbose = true;
  
  try {
    console.log('🔍 开始FTP连接测试...');
    
    // 基本连接配置
    const config = {
      host: '**************',
      user: 'yulingjing',
      password: 'readnewsjpg123',
      secure: false
    };
    
    console.log('📡 连接配置:', {
      host: config.host,
      user: config.user,
      password: '***',
      secure: config.secure
    });
    
    // 尝试连接
    console.log('🔌 正在连接...');
    await client.access(config);
    
    console.log('✅ 连接成功!');
    
    // 获取当前目录
    console.log('📁 获取当前目录...');
    const currentDir = await client.pwd();
    console.log('📂 当前目录:', currentDir);
    
    // 列出文件
    console.log('📋 列出文件...');
    const list = await client.list();
    console.log('📄 文件列表:');
    list.forEach((item, index) => {
      const type = item.type === 1 ? '📁' : '📄';
      console.log(`  ${index + 1}. ${type} ${item.name} (${item.size} bytes)`);
    });
    
    // 如果有文件，尝试下载第一个图片文件
    const imageFiles = list.filter(item => {
      if (item.type !== 2) return false; // 只要文件
      const ext = item.name.toLowerCase().split('.').pop();
      return ['jpg', 'jpeg', 'png', 'gif'].includes(ext);
    });
    
    if (imageFiles.length > 0) {
      const firstImage = imageFiles[0];
      console.log(`🖼️  尝试下载图片: ${firstImage.name}`);
      
      const chunks = [];
      await client.downloadTo({
        write: (chunk) => chunks.push(chunk),
        end: () => {},
        destroy: () => {}
      }, firstImage.name);
      
      const buffer = Buffer.concat(chunks);
      console.log(`✅ 下载成功! 大小: ${buffer.length} bytes`);
      
      // 检查文件头
      if (buffer.length > 10) {
        const header = buffer.slice(0, 10);
        console.log(`📊 文件头: ${header.toString('hex')}`);
      }
    } else {
      console.log('⚠️  当前目录没有找到图片文件');
    }
    
  } catch (error) {
    console.error('❌ FTP测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    try {
      client.close();
      console.log('🔌 连接已关闭');
    } catch (e) {
      console.error('关闭连接时出错:', e.message);
    }
  }
}

// 运行测试
simpleFTPTest().catch(console.error);
