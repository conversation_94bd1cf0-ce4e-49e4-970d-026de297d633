#!/usr/bin/env node

/**
 * SFTP连接测试脚本
 */

const SftpClient = require('ssh2-sftp-client');

// SFTP服务器配置
const SFTP_CONFIG = {
  host: '**************',
  port: 22,
  username: 'yulingjing',
  password: 'readnewsjpg123',
  readyTimeout: 20000,
  retries: 3
};

async function testSFTPConnection() {
  console.log('🚀 SFTP连接测试开始\n');
  
  const sftp = new SftpClient();
  
  try {
    console.log('📡 连接配置:');
    console.log(`   主机: ${SFTP_CONFIG.host}`);
    console.log(`   端口: ${SFTP_CONFIG.port}`);
    console.log(`   用户: ${SFTP_CONFIG.username}`);
    console.log(`   密码: ${'*'.repeat(SFTP_CONFIG.password.length)}`);
    console.log('');
    
    // 连接到SFTP服务器
    console.log('🔌 正在连接SFTP服务器...');
    await sftp.connect(SFTP_CONFIG);
    console.log('✅ SFTP连接成功!\n');
    
    // 获取当前工作目录
    console.log('📁 获取当前工作目录...');
    const cwd = await sftp.cwd();
    console.log(`📂 当前目录: ${cwd}\n`);
    
    // 列出根目录文件
    console.log('📋 列出根目录文件...');
    const rootList = await sftp.list('./');
    console.log(`📄 找到 ${rootList.length} 个项目:`);
    
    rootList.slice(0, 10).forEach((item, index) => {
      const type = item.type === 'd' ? '📁' : '📄';
      const size = item.type === 'd' ? '' : ` (${formatBytes(item.size)})`;
      console.log(`   ${index + 1}. ${type} ${item.name}${size}`);
    });
    
    if (rootList.length > 10) {
      console.log(`   ... 还有 ${rootList.length - 10} 个项目`);
    }
    console.log('');
    
    // 查找图片文件
    console.log('🔍 查找图片文件...');
    const imageFiles = await findImageFiles(sftp, './', 0, 2);
    
    if (imageFiles.length > 0) {
      console.log(`🖼️  找到 ${imageFiles.length} 个图片文件:`);
      imageFiles.slice(0, 5).forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.path} (${formatBytes(file.size)})`);
      });
      
      // 尝试下载第一个图片
      if (imageFiles.length > 0) {
        await testImageDownload(sftp, imageFiles[0]);
      }
    } else {
      console.log('⚠️  没有找到图片文件');
    }
    
  } catch (error) {
    console.error('❌ SFTP连接失败:', error.message);
    
    if (error.code) {
      console.error(`错误代码: ${error.code}`);
    }
    
    // 提供一些常见错误的解决建议
    if (error.message.includes('ENOTFOUND')) {
      console.log('\n💡 建议: 检查主机名或IP地址是否正确');
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 建议: 检查端口是否正确，服务器是否运行SSH服务');
    } else if (error.message.includes('Authentication')) {
      console.log('\n💡 建议: 检查用户名和密码是否正确');
    }
    
  } finally {
    try {
      await sftp.end();
      console.log('\n🔌 SFTP连接已关闭');
    } catch (e) {
      console.error('关闭连接时出错:', e.message);
    }
  }
}

async function findImageFiles(sftp, dir, currentDepth, maxDepth) {
  const imageFiles = [];
  
  if (currentDepth >= maxDepth) {
    return imageFiles;
  }
  
  try {
    const list = await sftp.list(dir);
    
    for (const item of list) {
      const fullPath = dir === './' ? item.name : `${dir}/${item.name}`;
      
      if (item.type === 'd' && currentDepth < maxDepth - 1) {
        // 递归搜索子目录
        const subImages = await findImageFiles(sftp, fullPath, currentDepth + 1, maxDepth);
        imageFiles.push(...subImages);
      } else if (item.type === '-') {
        // 检查是否是图片文件
        const ext = item.name.toLowerCase().split('.').pop();
        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext)) {
          imageFiles.push({
            path: fullPath,
            name: item.name,
            size: item.size,
            ext: ext
          });
        }
      }
    }
  } catch (error) {
    console.log(`⚠️  无法访问目录 ${dir}: ${error.message}`);
  }
  
  return imageFiles;
}

async function testImageDownload(sftp, imageFile) {
  console.log(`\n📥 测试下载图片: ${imageFile.path}`);
  
  try {
    const buffer = await sftp.get(imageFile.path);
    
    if (buffer && buffer.length > 0) {
      console.log(`✅ 图片下载成功! 大小: ${formatBytes(buffer.length)}`);
      
      // 检查图片头部信息
      if (buffer.length > 10) {
        const header = buffer.slice(0, 10);
        console.log(`📊 文件头: ${header.toString('hex')}`);
        
        // 简单的图片格式检测
        if (header[0] === 0xFF && header[1] === 0xD8) {
          console.log('🎯 检测到JPEG格式');
        } else if (header[0] === 0x89 && header[1] === 0x50) {
          console.log('🎯 检测到PNG格式');
        } else if (header[0] === 0x47 && header[1] === 0x49) {
          console.log('🎯 检测到GIF格式');
        } else {
          console.log('❓ 未知图片格式');
        }
      }
      
      return true;
    } else {
      console.log('❌ 下载的文件为空');
      return false;
    }
    
  } catch (error) {
    console.error(`❌ 图片下载失败: ${error.message}`);
    return false;
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function testProxyServer() {
  console.log('\n🌐 测试SFTP代理服务器...');
  
  try {
    const response = await fetch('http://localhost:3002/health');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 代理服务器运行正常:', data);
      
      // 测试连接测试接口
      console.log('\n🔧 测试代理服务器的SFTP连接...');
      const testResponse = await fetch('http://localhost:3002/test-connection');
      const testData = await testResponse.json();
      
      if (testData.success) {
        console.log('✅ 代理服务器SFTP连接正常');
        console.log(`📁 找到 ${testData.fileCount} 个文件`);
      } else {
        console.log('❌ 代理服务器SFTP连接失败:', testData.error);
      }
      
    } else {
      console.log('⚠️  代理服务器响应异常:', response.status);
    }
  } catch (error) {
    console.log('❌ 代理服务器不可用:', error.message);
    console.log('💡 请先启动SFTP代理服务器: node sftp-image-proxy-server.js');
  }
}

// 主函数
async function main() {
  await testSFTPConnection();
  await testProxyServer();
  
  console.log('\n✨ 测试完成!');
  console.log('\n📝 下一步:');
  console.log('1. 如果SFTP连接成功，启动代理服务器: node sftp-image-proxy-server.js');
  console.log('2. 在前端启用SFTP代理: 设置 ENABLE_FTP_PROXY = true');
  console.log('3. 测试图片显示效果');
}

if (require.main === module) {
  main().catch(console.error);
}
